"""enable pgvector, add document.embedding

Revision ID: be626c28a197
Revises: 40b5c244f6d9
Create Date: 2025-04-22 12:43:34.407616

"""
from typing import Sequence, Union

import pgvector
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'be626c28a197'
down_revision: Union[str, None] = '40b5c244f6d9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.execute('CREATE EXTENSION IF NOT EXISTS vector')
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('document', sa.Column('embedding', pgvector.sqlalchemy.vector.VECTOR(dim=768), nullable=True))
    op.create_index('idx_embedding', 'document', ['embedding'], unique=False, postgresql_using='ivfflat', postgresql_with={'lists': 100})
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_embedding', table_name='document', postgresql_using='ivfflat', postgresql_with={'lists': 100})
    op.drop_column('document', 'embedding')
    # ### end Alembic commands ###
