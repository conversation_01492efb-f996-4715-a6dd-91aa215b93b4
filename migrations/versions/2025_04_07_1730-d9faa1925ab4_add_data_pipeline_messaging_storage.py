"""add data pipeline messaging storage

Revision ID: d9faa1925ab4
Revises: cb84167f9436
Create Date: 2025-04-07 17:30:21.433452

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd9faa1925ab4'
down_revision: Union[str, None] = 'cb84167f9436'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('messaging_raw_data',
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('message_id', sa.String(), nullable=False),
    sa.Column('channel', sa.String(), nullable=False),
    sa.Column('raw_data', sa.Text(), nullable=False),
    sa.Column('hash', sa.String(), nullable=False),
    sa.Column('author', sa.String(), nullable=True),
    sa.Column('thread_id', sa.String(), nullable=True),
    sa.Column('tombstone', sa.Boolean(), nullable=False),
    sa.Column('sent_at', sa.DateTime(), nullable=False),
    sa.Column('last_edit_at', sa.DateTime(), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenant_id', 'channel', 'message_id', name='uq_tenant_channel_message')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('messaging_raw_data')
    # ### end Alembic commands ###
