"""rename messaging_raw_data.channel to messaging_raw_data.channel_id

Revision ID: eb6e173646cd
Revises: d9faa1925ab4
Create Date: 2025-04-08 12:06:51.180516

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'eb6e173646cd'
down_revision: Union[str, None] = 'd9faa1925ab4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.alter_column(
        'messaging_raw_data',
        'channel',
        new_column_name='channel_id',
        existing_type=sa.String(),
        nullable=False
    )
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uq_tenant_channel_message', 'messaging_raw_data', type_='unique')
    op.create_unique_constraint('uq_tenant_channel_message', 'messaging_raw_data', ['tenant_id', 'channel_id', 'message_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    op.alter_column(
        'messaging_raw_data',
        'channel_id',
        new_column_name='channel',
        existing_type=sa.String(),
        nullable=False
    )
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uq_tenant_channel_message', 'messaging_raw_data', type_='unique')
    op.create_unique_constraint('uq_tenant_channel_message', 'messaging_raw_data', ['tenant_id', 'channel', 'message_id'])
    # ### end Alembic commands ###
