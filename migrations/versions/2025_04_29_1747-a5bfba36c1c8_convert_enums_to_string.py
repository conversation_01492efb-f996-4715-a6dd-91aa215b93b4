"""convert enums to string

Revision ID: a5bfba36c1c8
Revises: a4148f84371e
Create Date: 2025-04-29 17:47:55.296923

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a5bfba36c1c8'
down_revision: Union[str, None] = 'a4148f84371e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('environment', 'type',
               existing_type=postgresql.ENUM('sandbox', 'prod', name='environmenttype'),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('integration_config', 'provider',
               existing_type=postgresql.ENUM('slack', 'salesforce', name='integrationprovider'),
               type_=sa.String(),
               existing_nullable=False)
    # ### end Alembic commands ###

    # Drop the enum types
    op.execute('DROP TYPE environmenttype')
    op.execute('DROP TYPE integrationprovider')


def downgrade():
    op.execute("CREATE TYPE environmenttype AS ENUM ('sandbox', 'prod')")
    op.execute("CREATE TYPE integrationprovider AS ENUM ('slack', 'salesforce')")

    op.execute("ALTER TABLE integration_config ALTER COLUMN provider TYPE integrationprovider USING provider::integrationprovider")
    op.execute("ALTER TABLE environment ALTER COLUMN type TYPE environmenttype USING type::environmenttype")