"""message_changelog table

Revision ID: 6626a567fbca
Revises: 13c8f35302e8
Create Date: 2025-04-15 13:09:58.240800

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6626a567fbca'
down_revision: Union[str, None] = '13c8f35302e8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('message_changelog',
    sa.Column('operation', sa.CHAR(length=1), nullable=False),
    sa.Column('message_id', sa.String(), nullable=False),
    sa.Column('channel_id', sa.String(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('source_id', sa.String(), nullable=False),
    sa.Column('change_timestamp', sa.DateTime(timezone=True), nullable=False),
    sa.Column('cursor_id', sa.BigInteger(), sa.Identity(always=True), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_message_changelog_cursor', 'message_changelog', ['cursor_id'], unique=False)
    op.create_index('idx_message_changelog_tenant', 'message_changelog', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_message_changelog_cursor_id'), 'message_changelog', ['cursor_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_message_changelog_cursor_id'), table_name='message_changelog')
    op.drop_index('idx_message_changelog_tenant', table_name='message_changelog')
    op.drop_index('idx_message_changelog_cursor', table_name='message_changelog')
    op.drop_table('message_changelog')
    # ### end Alembic commands ###
