"""rename document.document_timestamp to document.source_timestamp

Revision ID: 049c13b59611
Revises: 73d8ee85f24d
Create Date: 2025-04-21 06:17:58.496827

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '049c13b59611'
down_revision: Union[str, None] = '73d8ee85f24d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.alter_column(
        'document',
        'document_timestamp',
        new_column_name='source_timestamp',
        existing_type=sa.DateTime(timezone=True),
        nullable=False
    )


def downgrade() -> None:
    """Downgrade schema."""
    op.alter_column(
        'document',
        'source_timestamp',
        new_column_name='document_timestamp',
        existing_type=sa.DateTime(timezone=True),
        nullable=False
    )
