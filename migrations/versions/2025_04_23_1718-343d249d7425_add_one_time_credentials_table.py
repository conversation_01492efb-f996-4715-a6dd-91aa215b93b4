"""Add one_time_credentials table

Revision ID: 343d249d7425
Revises: be626c28a197
Create Date: 2025-04-23 17:18:37.991362

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '343d249d7425'
down_revision: Union[str, None] = 'be626c28a197'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('one_time_credentials',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('token', sa.String(length=1024), nullable=False),
    sa.Column('code', sa.String(length=6), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('used_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_one_time_credentials_token'), 'one_time_credentials', ['token'], unique=False)
    op.create_index(op.f('ix_one_time_credentials_user_id'), 'one_time_credentials', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_one_time_credentials_user_id'), table_name='one_time_credentials')
    op.drop_index(op.f('ix_one_time_credentials_token'), table_name='one_time_credentials')
    op.drop_table('one_time_credentials')
    # ### end Alembic commands ###
