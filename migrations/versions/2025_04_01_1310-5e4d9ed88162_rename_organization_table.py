"""rename organization table

Revision ID: 5e4d9ed88162
Revises: 127faff3a8c3
Create Date: 2025-04-01 13:10:26.807737

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5e4d9ed88162'
down_revision: Union[str, None] = '127faff3a8c3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.rename_table('organizations', 'organization')
    op.execute('ALTER INDEX organizations_pkey RENAME TO organization_pkey')

def downgrade() -> None:
    op.rename_table('organization', 'organizations')
    op.execute('ALTER INDEX organization_pkey RENAME TO organizations_pkey')
