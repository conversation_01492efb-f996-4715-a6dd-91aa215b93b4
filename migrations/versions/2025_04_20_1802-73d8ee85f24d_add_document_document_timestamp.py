"""add document.document_timestamp

Revision ID: 73d8ee85f24d
Revises: 6d0803886cc5
Create Date: 2025-04-20 18:02:17.795583

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '73d8ee85f24d'
down_revision: Union[str, None] = '6d0803886cc5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('document', sa.Column('document_timestamp', sa.DateTime(timezone=True), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('document', 'document_timestamp')
    # ### end Alembic commands ###
