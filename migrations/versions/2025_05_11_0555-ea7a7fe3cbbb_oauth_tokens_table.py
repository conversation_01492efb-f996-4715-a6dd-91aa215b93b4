"""OAuth tokens table

Revision ID: ea7a7fe3cbbb
Revises: 21165345613c
Create Date: 2025-05-11 05:55:44.068680

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ea7a7fe3cbbb'
down_revision: Union[str, None] = '21165345613c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('oauth_tokens',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('integration_config_id', sa.UUID(), nullable=False),
    sa.Column('external_user_id', sa.String(), nullable=False),
    sa.Column('external_org_id', sa.String(), nullable=True),
    sa.Column('access_token', sa.String(), nullable=False),
    sa.Column('refresh_token', sa.String(), nullable=False),
    sa.Column('instance_url', sa.String(), nullable=True),
    sa.Column('scope', sa.String(), nullable=True),
    sa.Column('token_type', sa.String(), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_refreshed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['integration_config_id'], ['integration_config.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'integration_config_id', name='uq_user_integration_config')
    )
    op.create_index(op.f('ix_oauth_tokens_integration_config_id'), 'oauth_tokens', ['integration_config_id'], unique=False)
    op.create_index(op.f('ix_oauth_tokens_user_id'), 'oauth_tokens', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_oauth_tokens_user_id'), table_name='oauth_tokens')
    op.drop_index(op.f('ix_oauth_tokens_integration_config_id'), table_name='oauth_tokens')
    op.drop_table('oauth_tokens')
    # ### end Alembic commands ###
