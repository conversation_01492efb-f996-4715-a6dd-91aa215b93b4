"""Add thread name

Revision ID: 45fc7d821c95
Revises: f1a8114f95a5
Create Date: 2025-07-04 14:31:12.462860

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '45fc7d821c95'
down_revision: Union[str, None] = 'f1a8114f95a5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organization_member_threads', sa.Column('thread_name', sa.String(length=255), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('organization_member_threads', 'thread_name')
    # ### end Alembic commands ###
