"""Add file processing run table

Revision ID: 2bf79239569e
Revises: 9d337e98fca6
Create Date: 2025-06-17 17:03:14.662652

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2bf79239569e'
down_revision: Union[str, None] = '9d337e98fca6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('file_processing_run',
    sa.Column('source', sa.String(), nullable=False),
    sa.Column('bucket_name', sa.String(), nullable=False),
    sa.Column('extensions', sa.ARRAY(sa.String()), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('files_processed', sa.Integer(), nullable=False),
    sa.Column('deleted_documents', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.String(length=1024), nullable=True),
    sa.Column('run_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('run_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('file_processing_run')
    # ### end Alembic commands ###
