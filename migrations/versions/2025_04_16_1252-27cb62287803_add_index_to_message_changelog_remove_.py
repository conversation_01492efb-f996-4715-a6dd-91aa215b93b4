"""add index to message_changelog, remove useless columns

Revision ID: 27cb62287803
Revises: a3c15daa3c8a
Create Date: 2025-04-16 12:52:56.604324

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '27cb62287803'
down_revision: Union[str, None] = 'a3c15daa3c8a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_message_changelog_cursor', table_name='message_changelog')
    op.drop_index('idx_message_changelog_tenant', table_name='message_changelog')
    op.create_index('idx_message_changelog_tenant_channel_cursor', 'message_changelog', ['tenant_id', 'channel_id', 'cursor_id'], unique=False)
    op.create_index('idx_message_changelog_tenant_cursor', 'message_changelog', ['tenant_id', 'cursor_id'], unique=False)
    op.drop_column('message_changelog', 'change_timestamp')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('message_changelog', sa.Column('change_timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.drop_index('idx_message_changelog_tenant_cursor', table_name='message_changelog')
    op.drop_index('idx_message_changelog_tenant_channel_cursor', table_name='message_changelog')
    op.create_index('idx_message_changelog_tenant', 'message_changelog', ['tenant_id'], unique=False)
    op.create_index('idx_message_changelog_cursor', 'message_changelog', ['cursor_id'], unique=False)
    # ### end Alembic commands ###
