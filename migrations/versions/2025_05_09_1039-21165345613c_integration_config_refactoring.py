"""integration_config refactoring

Revision ID: 21165345613c
Revises: b1431a4adec5
Create Date: 2025-05-09 10:39:24.680779

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text

# revision identifiers, used by Alembic.
revision: str = '21165345613c'
down_revision: Union[str, None] = 'b1431a4adec5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade():
    op.add_column('integration_config', sa.Column('settings', sa.JSON(), nullable=True))
    op.add_column('integration_config', sa.Column('integration_type', sa.String(), nullable=True))

    op.execute(text("UPDATE integration_config SET integration_type = 'messaging' WHERE source = 'slack'"))
    op.execute(text("UPDATE integration_config SET integration_type = 'crm' WHERE integration_type IS NULL"))

    op.alter_column('integration_config', 'integration_type', nullable=False)
    op.alter_column('integration_config', 'config', new_column_name='credentials')


def downgrade():
    op.alter_column('integration_config', 'credentials', new_column_name='config')
    op.drop_column('integration_config', 'settings')
    op.drop_column('integration_config', 'integration_type')