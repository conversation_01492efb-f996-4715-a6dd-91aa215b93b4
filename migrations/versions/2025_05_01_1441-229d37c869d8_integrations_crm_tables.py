"""integrations crm tables

Revision ID: 229d37c869d8
Revises: fcdaec471dfa
Create Date: 2025-05-01 14:41:11.494122

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '229d37c869d8'
down_revision: Union[str, None] = 'fcdaec471dfa'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('crm_account_access',
    sa.Column('provider', sa.String(length=20), nullable=False),
    sa.Column('crm_user_id', sa.String(length=50), nullable=False),
    sa.Column('crm_account_id', sa.String(length=50), nullable=False),
    sa.Column('crm_account_name', sa.String(length=255), nullable=False),
    sa.Column('crm_access_type', sa.String(length=100), nullable=False),
    sa.Column('crm_access_role', sa.String(length=100), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenant_id', 'provider', 'crm_user_id', 'crm_account_id', name='uq_crm_access_user_account')
    )
    op.create_index(op.f('ix_crm_account_access_crm_account_id'), 'crm_account_access', ['crm_account_id'], unique=False)
    op.create_index(op.f('ix_crm_account_access_crm_user_id'), 'crm_account_access', ['crm_user_id'], unique=False)
    op.create_table('salesforce_access_sync_run',
    sa.Column('salesforce_user_id', sa.String(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('new_access_count', sa.Integer(), nullable=False),
    sa.Column('old_access_count', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.String(length=1024), nullable=True),
    sa.Column('run_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('run_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('salesforce_access_sync_run')
    op.drop_index(op.f('ix_crm_account_access_crm_user_id'), table_name='crm_account_access')
    op.drop_index(op.f('ix_crm_account_access_crm_account_id'), table_name='crm_account_access')
    op.drop_table('crm_account_access')
    # ### end Alembic commands ###
