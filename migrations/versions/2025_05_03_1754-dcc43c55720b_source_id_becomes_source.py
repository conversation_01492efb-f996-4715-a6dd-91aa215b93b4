"""source_id becomes source

Revision ID: dcc43c55720b
Revises: 526fec73a3f3
Create Date: 2025-05-03 17:54:12.247419

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dcc43c55720b'
down_revision: Union[str, None] = '526fec73a3f3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.drop_index('idx_tenant_source_id', table_name='document')
    op.drop_index('ix_document_source_id', table_name='document')
    op.drop_constraint('uq_tenant_source_message', 'message_raw_data', type_='unique')

    op.alter_column('document', 'source_id',
                    new_column_name='source',
                    existing_type=sa.String(),
                    type_=sa.String(),
                    existing_nullable=False)

    op.alter_column('message_changelog', 'source_id',
                    new_column_name='source',
                    existing_type=sa.String(),
                    type_=sa.String(),
                    existing_nullable=False)

    op.alter_column('message_raw_data', 'source_id',
                    new_column_name='source',
                    existing_type=sa.Text(),
                    type_=sa.String(),
                    existing_nullable=False)

    op.create_index('idx_tenant_source', 'document', ['tenant_id', 'source'], unique=False)
    op.create_index(op.f('ix_document_source'), 'document', ['source'], unique=False)
    op.create_unique_constraint('uq_tenant_source_message', 'message_raw_data', ['tenant_id', 'source', 'message_id'])


def downgrade() -> None:
    op.drop_constraint('uq_tenant_source_message', 'message_raw_data', type_='unique')
    op.drop_index(op.f('ix_document_source'), table_name='document')
    op.drop_index('idx_tenant_source', table_name='document')

    op.alter_column('document', 'source',
                    new_column_name='source_id',
                    existing_type=sa.String(),
                    type_=sa.String(),
                    existing_nullable=False)

    op.alter_column('message_changelog', 'source',
                    new_column_name='source_id',
                    existing_type=sa.String(),
                    type_=sa.String(),
                    existing_nullable=False)

    op.alter_column('message_raw_data', 'source',
                    new_column_name='source_id',
                    existing_type=sa.String(),
                    type_=sa.Text(),
                    existing_nullable=False)

    op.create_unique_constraint('uq_tenant_source_message', 'message_raw_data', ['tenant_id', 'source_id', 'message_id'])
    op.create_index('ix_document_source_id', 'document', ['source_id'], unique=False)
    op.create_index('idx_tenant_source_id', 'document', ['tenant_id', 'source_id'], unique=False)
