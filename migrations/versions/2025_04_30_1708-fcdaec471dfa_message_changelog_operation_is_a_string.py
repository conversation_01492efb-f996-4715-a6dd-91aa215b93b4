"""message_changelog.operation is a string

Revision ID: fcdaec471dfa
Revises: 6b7652df1c61
Create Date: 2025-04-30 17:08:08.954921

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fcdaec471dfa'
down_revision: Union[str, None] = '6b7652df1c61'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('message_changelog', 'operation',
               existing_type=sa.CHAR(length=1),
               type_=sa.String(length=1),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('message_changelog', 'operation',
               existing_type=sa.String(length=1),
               type_=sa.CHAR(length=1),
               existing_nullable=False)
    # ### end Alembic commands ###
