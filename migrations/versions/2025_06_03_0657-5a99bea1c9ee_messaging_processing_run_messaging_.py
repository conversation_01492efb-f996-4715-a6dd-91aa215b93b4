"""messaging_processing_run & messaging_ingestion_run tables

Revision ID: 5a99bea1c9ee
Revises: bd144046d1a2
Create Date: 2025-06-03 06:57:24.969899

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5a99bea1c9ee'
down_revision: Union[str, None] = 'bd144046d1a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('messaging_ingestion_run',
    sa.Column('source', sa.String(), nullable=False),
    sa.Column('channel_id', sa.String(), nullable=False),
    sa.Column('slice_from_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('slice_to_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('messages_processed', sa.Integer(), nullable=False),
    sa.Column('inserts', sa.Integer(), nullable=False),
    sa.Column('updates', sa.Integer(), nullable=False),
    sa.Column('deletes', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.String(length=1024), nullable=True),
    sa.Column('run_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('run_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('messaging_processing_run',
    sa.Column('source', sa.String(), nullable=False),
    sa.Column('channel_id', sa.String(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('processed_changes', sa.Integer(), nullable=False),
    sa.Column('regenerated_documents', sa.Integer(), nullable=False),
    sa.Column('deleted_documents', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.String(length=1024), nullable=True),
    sa.Column('run_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('run_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('slack_processing_run')
    op.drop_table('slack_ingestion_run')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('slack_ingestion_run',
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('channel_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('slice_from_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('slice_to_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('status', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('messages_processed', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('inserts', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('updates', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('deletes', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('error_message', sa.VARCHAR(length=1024), autoincrement=False, nullable=True),
    sa.Column('run_start', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('run_end', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('slack_ingestion_run_pkey'))
    )
    op.create_table('slack_processing_run',
    sa.Column('channel_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('status', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('processed_changes', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('regenerated_documents', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('deleted_documents', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('error_message', sa.VARCHAR(length=1024), autoincrement=False, nullable=True),
    sa.Column('run_start', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('run_end', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('slack_processing_run_pkey'))
    )
    op.drop_table('messaging_processing_run')
    op.drop_table('messaging_ingestion_run')
    # ### end Alembic commands ###
