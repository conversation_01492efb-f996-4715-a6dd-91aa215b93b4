"""add organization member thread table

Revision ID: e8194b2e8ed8
Revises: ea7a7fe3cbbb
Create Date: 2025-05-20 18:07:43.414773

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e8194b2e8ed8'
down_revision: Union[str, None] = 'ea7a7fe3cbbb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('organization_member_threads',
    sa.Column('thread_id', sa.Text(), nullable=False),
    sa.Column('organization_member_id', sa.UUID(), nullable=False),
    sa.Column('environment_id', sa.UUID(), nullable=False),
    sa.Column('crm_account_id', sa.String(length=50), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['organization_member_id'], ['organization_member.id']),
    sa.ForeignKeyConstraint(['environment_id'], ['environment.id']),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('thread_id', 'organization_member_id', 'environment_id', name='uq_thread_organization_member_environment')
    )
    op.create_index(op.f('ix_organization_member_threads_thread_id'), 'organization_member_threads', ['thread_id'], unique=True)
    op.create_index(op.f('ix_organization_member_threads_organization_member_id'), 'organization_member_threads', ['organization_member_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_organization_member_threads_thread_id'), table_name='organization_member_threads')
    op.drop_index(op.f('ix_organization_member_threads_organization_member_id'), table_name='organization_member_threads')
    op.drop_table('organization_member_threads')
    # ### end Alembic commands ###
