"""rename oauth_token en integration_user

Revision ID: 9d337e98fca6
Revises: 4e49f8c45cc8
Create Date: 2025-06-14 07:39:41.972852

"""
from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = '9d337e98fca6'
down_revision: Union[str, None] = '4e49f8c45cc8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.rename_table('oauth_tokens', 'integration_user')

    op.execute('ALTER INDEX ix_oauth_tokens_integration_config_id RENAME TO ix_integration_user_integration_config_id')
    op.execute('ALTER INDEX ix_oauth_tokens_user_id RENAME TO ix_integration_user_user_id')

    op.execute(
        'ALTER TABLE integration_user RENAME CONSTRAINT oauth_tokens_integration_config_id_fkey TO integration_user_integration_config_id_fkey')
    op.execute(
        'ALTER TABLE integration_user RENAME CONSTRAINT oauth_tokens_user_id_fkey TO integration_user_user_id_fkey')

    op.execute('ALTER TABLE integration_user RENAME CONSTRAINT oauth_tokens_pkey TO integration_user_pkey')


def downgrade() -> None:
    op.rename_table('integration_user', 'oauth_tokens')

    op.execute('ALTER INDEX ix_integration_user_integration_config_id RENAME TO ix_oauth_tokens_integration_config_id')
    op.execute('ALTER INDEX ix_integration_user_user_id RENAME TO ix_oauth_tokens_user_id')

    op.execute(
        'ALTER TABLE oauth_tokens RENAME CONSTRAINT integration_user_integration_config_id_fkey TO oauth_tokens_integration_config_id_fkey')
    op.execute('ALTER TABLE oauth_tokens RENAME CONSTRAINT integration_user_user_id_fkey TO oauth_tokens_user_id_fkey')

    op.execute('ALTER TABLE oauth_tokens RENAME CONSTRAINT integration_user_pkey TO oauth_tokens_pkey')