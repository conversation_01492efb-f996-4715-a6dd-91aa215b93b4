"""create uq_message_raw_data unique contraint(tenant_id,source_id,message_id)

Revision ID: 40b5c244f6d9
Revises: 522aa70383a2
Create Date: 2025-04-21 14:34:57.042748

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '40b5c244f6d9'
down_revision: Union[str, None] = '522aa70383a2'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uq_tenant_message', 'message_raw_data', type_='unique')
    op.create_unique_constraint('uq_tenant_source_message', 'message_raw_data', ['tenant_id', 'source_id', 'message_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uq_tenant_source_message', 'message_raw_data', type_='unique')
    op.create_unique_constraint('uq_tenant_message', 'message_raw_data', ['tenant_id', 'message_id'])
    # ### end Alembic commands ###
