"""create slack_processing_run

Revision ID: 522aa70383a2
Revises: 049c13b59611
Create Date: 2025-04-21 14:01:54.553314

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '522aa70383a2'
down_revision: Union[str, None] = '049c13b59611'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('slack_processing_run',
    sa.Column('channel_id', sa.String(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('processed_changes', sa.Integer(), nullable=False),
    sa.Column('regenerated_documents', sa.Integer(), nullable=False),
    sa.Column('deleted_documents', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.String(length=1024), nullable=True),
    sa.Column('run_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('run_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('slack_processing_run')
    # ### end Alembic commands ###
