"""add quota defaults

Revision ID: 326e8bfada42
Revises: f42b0ca571cf
Create Date: 2025-07-15 18:42:42.874404

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '326e8bfada42'
down_revision: Union[str, None] = 'f42b0ca571cf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('salesforce_field_mapping', 'quota_object',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('salesforce_field_mapping', 'quota_amount_field',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('salesforce_field_mapping', 'quota_user_field',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('salesforce_field_mapping', 'quota_user_field',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('salesforce_field_mapping', 'quota_amount_field',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    op.alter_column('salesforce_field_mapping', 'quota_object',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    # ### end Alembic commands ###
