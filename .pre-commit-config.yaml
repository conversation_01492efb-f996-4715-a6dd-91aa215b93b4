fail_fast: true

repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.11.1
    hooks:
      # Run the linter.
      - id: ruff
        types_or: [ python, pyi ]
        args: [ --fix ]
      # Run the formatter.
      - id: ruff-format
        types_or: [ python, pyi ]
  - repo: local
    hooks:
      - id: mypy
        name: mypy
        entry: poetry run mypy
        language: system
        types_or: [ python, pyi ]
        exclude: ^(migrations/)
