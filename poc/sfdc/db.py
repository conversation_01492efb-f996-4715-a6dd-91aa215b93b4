from datetime import datetime
from sqlite3 import Connection, Row, connect


def init_db(db_path="local_sfdc.db") -> Connection:
    con = connect(db_path, check_same_thread=False)
    con.row_factory = Row
    cursor = con.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS sfdc_object (
            id TEXT PRIMARY KEY,
            object TEXT,
            data TEXT,
            created_at TEXT,
            updated_at TEXT
        )
    """)
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS sfdc_object_fields (
            id TEXT PRIMARY KEY,
            object TEXT,
            fields TEXT,
            created_at TEXT,
            updated_at TEXT
        )
    """)
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS sfdc_sync (
            id TEXT PRIMARY KEY,
            last_synced_at TEXT,
            created_at TEXT,
            updated_at TEXT
        )
    """)
    # insert initial sync datetime far in the past
    cursor.execute("""
        INSERT OR IGNORE INTO sfdc_sync (id, last_synced_at, created_at, updated_at)
        VALUES ('0', '2000-01-01T00:00:00Z', '2000-01-01T00:00:00Z', '2000-01-01T00:00:00Z')
    """)
    con.commit()
    return con


def get_sfdc_object_fields(con: Connection, object_name: str) -> Row | None:
    cursor = con.cursor()
    cursor.execute(
        """
        SELECT id, fields
        FROM sfdc_object_fields
        WHERE object = ?
    """,
        (object_name,),
    )
    return cursor.fetchone()


def insert_sfdc_object_fields(
    con: Connection,
    id: str,
    object_name: str,
    fields: str,
    created_at: datetime,
    updated_at: datetime,
) -> None:
    cursor = con.cursor()
    cursor.execute(
        """
        INSERT INTO sfdc_object_fields (id, object, fields, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
    """,
        (id, object_name, fields, created_at, updated_at),
    )
    con.commit()


def update_sfdc_object_fields(
    con: Connection,
    id: str,
    fields: str,
    updated_at: datetime,
) -> None:
    cursor = con.cursor()
    cursor.execute(
        """
        UPDATE sfdc_object_fields
        SET fields = ?, updated_at = ?
        WHERE id = ?
    """,
        (fields, updated_at, id),
    )
    con.commit()


def get_sfdc_object(con: Connection, id: str) -> Row | None:
    cursor = con.cursor()
    cursor.execute(
        """
        SELECT id, data, object
        FROM sfdc_object
        WHERE id = ?
    """,
        (id,),
    )
    return cursor.fetchone()


def update_sfdc_object(
    con: Connection,
    id: str,
    object_name: str,
    data: str,
    updated_at: datetime,
) -> None:
    cursor = con.cursor()
    cursor.execute(
        """
        UPDATE sfdc_object
        SET object = ?, data = ?, updated_at = ?
        WHERE id = ?
    """,
        (object_name, data, updated_at, id),
    )
    con.commit()


def insert_sfdc_object(
    con: Connection,
    id: str,
    object_name: str,
    data: str,
    created_at: datetime,
    updated_at: datetime,
) -> None:
    cursor = con.cursor()
    cursor.execute(
        """
        INSERT INTO sfdc_object (id, object, data, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
    """,
        (id, object_name, data, created_at, updated_at),
    )
    con.commit()


def bulk_insert_objects(
    con: Connection,
    object_name: str,
    data: list[dict],
    created_at: datetime,
) -> None:
    cursor = con.cursor()
    cursor.executemany(
        """
        INSERT INTO sfdc_object (id, object, data, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
    """,
        [
            (record["id"], object_name, record["data"], created_at, created_at)
            for record in data
        ],
    )
    con.commit()


def delete_sfdc_object(con: Connection, id: str) -> None:
    cursor = con.cursor()
    cursor.execute(
        """
        DELETE FROM sfdc_object
        WHERE id = ?
    """,
        (id,),
    )
    con.commit()


def get_last_synced_at(con: Connection) -> str:
    cursor = con.cursor()
    cursor.execute(
        """
        SELECT last_synced_at
        FROM sfdc_sync
        WHERE id = '0'
    """
    )
    return cursor.fetchone()["last_synced_at"]


def update_last_synced_at(con: Connection, last_synced_at: datetime) -> None:
    cursor = con.cursor()
    cursor.execute(
        """
        UPDATE sfdc_sync
        SET last_synced_at = ?
        WHERE id = '0'
    """,
        (last_synced_at,),
    )
    con.commit()
