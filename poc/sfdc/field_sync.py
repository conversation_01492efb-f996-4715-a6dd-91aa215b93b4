from datetime import datetime
from json import dumps
from sqlite3 import Connection
from uuid import uuid4

import pytz

from poc.sfdc.data_fetcher import SalesforceDataFetcher, SalesforceTable
from poc.sfdc.db import (
    get_sfdc_object_fields,
    insert_sfdc_object_fields,
    update_sfdc_object_fields,
)


def sync_fields(con: Connection, data_fetcher: SalesforceDataFetcher) -> None:
    now = datetime.now(pytz.utc)
    for table, fields in [
        (SalesforceTable.ACCOUNT, data_fetcher.get_account_fields()),
        (SalesforceTable.CONTACT, data_fetcher.get_contact_fields()),
        (SalesforceTable.OPPORTUNITY, data_fetcher.get_opportunity_fields()),
        (SalesforceTable.NOTE, data_fetcher.get_note_fields()),
    ]:
        fields_as_json = dumps(
            [{"name": field.name, "label": field.label} for field in fields]
        )
        existing_fields_record = get_sfdc_object_fields(con, table.value)
        if existing_fields_record is not None:
            print(f"Updating fields for {table.value}")
            update_sfdc_object_fields(
                con=con,
                id=existing_fields_record["id"],
                fields=fields_as_json,
                updated_at=now,
            )
        else:
            print(f"Inserting fields for {table.value}")
            insert_sfdc_object_fields(
                con=con,
                id=str(uuid4()),
                object_name=table.value,
                fields=fields_as_json,
                created_at=now,
                updated_at=now,
            )
        print(f"Loaded {len(fields)} {table.value} fields")
