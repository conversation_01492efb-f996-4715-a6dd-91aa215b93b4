# AGENTS.md - Pearl Backend Development Guide

## Commands
- **Lint**: `ruff check .` (fix with `ruff check . --fix`)
- **Format**: `ruff format .`
- **Type check**: `mypy .`
- **Test all**: `pytest`
- **Test single file**: `pytest app/path/to/test_file.py`
- **Test single function**: `pytest app/path/to/test_file.py::test_function_name`

## Code Style
- **Line length**: 88 characters (ruff enforced)
- **Python version**: 3.12
- **Imports**: Use absolute imports only (no relative), combine as imports with `as`
- **Type hints**: Required for all function parameters and return types
- **Naming**: snake_case for variables/functions, PascalCase for classes
- **Error handling**: Use custom exceptions from `app.*.exceptions`, raise specific errors
- **Database**: Use SQLAlchemy async sessions, commit explicitly after operations
- **Dependencies**: Use FastAPI's `Depends()` for dependency injection
- **Logging**: Use `get_logger()` from `app.common.helpers.logger`
- **Tests**: Use pytest with async fixtures, place in `tests/` subdirectories
- **Models**: Use SQLAlchemy 2.0 style with `Mapped[]` annotations and `mapped_column()`
- **Services**: Accept repositories and dependencies via constructor injection