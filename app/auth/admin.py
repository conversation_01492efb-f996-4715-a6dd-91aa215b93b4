from app.admin import BaseModelView
from app.auth.models import OneTimeCredentials, RefreshToken, User


class _AuthModelView(BaseModelView):
    category = "Auth"


class UserAdmin(_AuthModelView, model=User):
    autodiscover_order = 10
    column_list = [
        User.id,
        User.last_name,
        User.first_name,
        User.email,
    ]


class RefreshTokenAdmin(_AuthModelView, model=RefreshToken):
    autodiscover_order = 20
    column_list = [
        RefreshToken.id,
        RefreshToken.user_id,
        RefreshToken.token,
        RefreshToken.expires_at,
    ]


class OneTimeCredentialsAdmin(_AuthModelView, model=OneTimeCredentials):
    autodiscover_order = 30
    column_list = [
        OneTimeCredentials.id,
        OneTimeCredentials.user_id,
        OneTimeCredentials.code,
        OneTimeCredentials.expires_at,
    ]
