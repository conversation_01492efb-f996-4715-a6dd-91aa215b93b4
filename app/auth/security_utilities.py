import secrets
from enum import Enum

import jwt

from app.auth.exceptions import AuthTokenDecodeError

# TODO: move consts to config
SECRET_KEY = "649e08d9d8e036e6de035fec3d90c8ff487ca6d021916f8bb8f110264d05dd1e"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7
MAX_REFRESH_TOKENS = 5
ONE_TIME_CREDENTIALS_EXPIRE_MINUTES = 10


class TokenType(Enum):
    ACCESS = "access"
    REFRESH = "refresh"
    LOGIN = "login"


def create_jwt_token(data: dict) -> str:
    """Create a JWT token."""
    return jwt.encode(data, SECRET_KEY, algorithm=ALGORITHM)


def decode_token(token: str, token_type: TokenType) -> dict:
    """Decode a JWT token."""
    try:
        token_content = jwt.decode(
            token, SECRET_KEY, algorithms=[ALGORITHM], options=dict(require_exp=True)
        )
    except jwt.DecodeError:
        raise AuthTokenDecodeError("Token is invalid.")
    except jwt.ExpiredSignatureError:
        raise AuthTokenDecodeError("Token has expired.")

    if token_content["type"] != token_type.value:
        raise AuthTokenDecodeError("Inconsistent token type.")
    return token_content


def generate_auth_code() -> str:
    """Generate a 6-digit auth code.
    Adding 100000 to a random number between 0 and 899999
    ensures that the result is always a 6-digit number.
    """
    return str(secrets.randbelow(900000) + 100000)
