from app.common.exceptions.base import UnauthorizedException


class AuthTokenDecodeError(UnauthorizedException):
    """Custom exception for token decoding errors."""

    pass


class AuthTokenRevocationError(UnauthorizedException):
    """Custom exception for token revocation errors."""

    pass


class OneTimeCredentialsTokenError(UnauthorizedException):
    """Custom exception for one-time credentials token errors."""

    pass


class OneTimeCredentialsCodeError(UnauthorizedException):
    """Custom exception for one-time credentials code errors."""

    pass
