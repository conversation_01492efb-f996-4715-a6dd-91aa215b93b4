from typing import Annotated
from uuid import UUI<PERSON>

from fastapi import BackgroundTasks, Depends
from fastapi.security import OAuth2<PERSON>ass<PERSON>Bearer

from app.auth.repository import (
    OneTimeCredentialsRepository,
    RefreshTokenRepository,
    UserRepository,
)
from app.auth.service import AuthService
from app.common.email.dependencies import EmailClientDep
from app.common.email.email_service import EmailService
from app.common.email.template_renderer import TemplateRenderer
from app.core.database import AsyncDbSessionDep


def get_auth_template_renderer() -> TemplateRenderer:
    return TemplateRenderer(template_dir="app/auth/email_templates")


AuthTemplateRendererDep = Annotated[
    Template<PERSON>enderer, Depends(get_auth_template_renderer)
]


def get_auth_email_service(
    template_renderer: AuthTemplateRendererDep,
    email_client: EmailClientDep,
    background_tasks: BackgroundTasks,
) -> EmailService:
    return EmailService(
        template_renderer=template_renderer,
        email_client=email_client,
        background_tasks=background_tasks,
    )


AuthEmailServiceDep = Annotated[EmailService, Depends(get_auth_email_service)]


def get_auth_service(
    db_session: AsyncDbSessionDep,
    email_service: AuthEmailServiceDep,
) -> AuthService:
    user_repository = UserRepository(db_session)
    refresh_token_repository = RefreshTokenRepository(db_session)
    one_time_credentials_repository = OneTimeCredentialsRepository(db_session)
    return AuthService(
        db_session=db_session,
        user_repository=user_repository,
        refresh_token_repository=refresh_token_repository,
        one_time_credentials_repository=one_time_credentials_repository,
        email_service=email_service,
    )


AuthServiceDep = Annotated[AuthService, Depends(get_auth_service)]

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

AccessTokenDep = Annotated[str, Depends(oauth2_scheme)]


async def get_authenticated_user_id(
    token: AccessTokenDep,
    auth_service: AuthServiceDep,
) -> UUID:
    return await auth_service.get_user_id_from_access_token(token)


AuthenticatedUserIdDep = Annotated[UUID, Depends(get_authenticated_user_id)]
