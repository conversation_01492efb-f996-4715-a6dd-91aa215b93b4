from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from app.workspace.types import EnvironmentType


class OrganizationRead(BaseModel):
    id: UUID
    name: str
    domain: str | None
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class OrgEnvironment(BaseModel):
    id: UUID
    organization_id: UUID
    type: EnvironmentType
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
