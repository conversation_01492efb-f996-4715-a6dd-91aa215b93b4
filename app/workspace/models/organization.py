import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON><PERSON>, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.auth.models import User
from app.core.database import BaseModel


class Organization(BaseModel):
    __tablename__ = "organization"

    name: Mapped[str] = mapped_column(String, nullable=False)
    domain: Mapped[str] = mapped_column(String, nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    owner_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("user.id"), nullable=False, index=True
    )

    owner: Mapped[User] = relationship(User)

    def __repr__(self):
        return f"<Organization(id='{self.id}', name='{self.name}')>"
