import uuid
from typing import TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>loat, <PERSON><PERSON><PERSON>, Integer, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import BaseModel

if TYPE_CHECKING:
    from app.workspace.models.organization import Organization


class SalesforceFieldMapping(BaseModel):
    """
    Stores organization-specific field mappings for Salesforce metrics retrieval.

    This allows different organizations to configure their Salesforce field names
    and stage patterns for accurate metrics calculation.
    """

    __tablename__ = "salesforce_field_mapping"

    organization_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("organization.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    opportunity_amount_field: Mapped[str] = mapped_column(
        String(255), nullable=False, default="Amount"
    )
    opportunity_stage_field: Mapped[str] = mapped_column(
        String(255), nullable=False, default="StageName"
    )
    opportunity_owner_field: Mapped[str] = mapped_column(
        String(255), nullable=False, default="OwnerId"
    )
    opportunity_probability_field: Mapped[str] = mapped_column(
        String(255), nullable=False, default="Probability"
    )

    closed_won_stage_pattern: Mapped[str] = mapped_column(
        String(255), nullable=False, default="%Closed Won%"
    )
    closed_lost_stage_pattern: Mapped[str] = mapped_column(
        String(255), nullable=False, default="%Closed Lost%"
    )

    forecast_probability_multiplier: Mapped[float] = mapped_column(
        Float, nullable=False, default=0.5
    )
    use_probability_field: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False
    )

    quota_object: Mapped[str] = mapped_column(
        String(255), nullable=False, default="ForecastingQuota"
    )
    quota_amount_field: Mapped[str] = mapped_column(
        String(255), nullable=False, default="QuotaAmount"
    )
    quota_user_field: Mapped[str] = mapped_column(
        String(255), nullable=False, default="QuotaOwnerId"
    )

    metrics_months_limit: Mapped[int] = mapped_column(
        Integer, nullable=False, default=3
    )

    organization: Mapped["Organization"] = relationship("Organization")

    __table_args__ = (
        UniqueConstraint(
            "organization_id", name="uq_salesforce_field_mapping_organization_id"
        ),
    )
