import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.common.orm.types import StringEnum
from app.core.database import BaseModel
from app.workspace.models.organization import Organization
from app.workspace.types import EnvironmentType


class Environment(BaseModel):
    __tablename__ = "environment"

    organization_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("organization.id"),
        nullable=False,
        index=True,
    )
    type: Mapped[EnvironmentType] = mapped_column(
        StringEnum(EnvironmentType),
        nullable=False,
    )
    organization: Mapped[Organization] = relationship("Organization")

    def __repr__(self):
        return f"<Environment(id='{self.id}', type='{self.type}')>"

    __table_args__ = (
        UniqueConstraint("organization_id", "type", name="uix_organization_type"),
    )
