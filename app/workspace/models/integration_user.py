import uuid
from datetime import datetime

from sqlalchemy import DateTime, ForeignKey, String, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.auth.models import User
from app.core.database import BaseModel
from app.workspace.models.integration_config import IntegrationConfig


class IntegrationUser(BaseModel):
    __tablename__ = "integration_user"

    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("user.id"),
        nullable=False,
        index=True,
    )

    integration_config_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("integration_config.id"),
        nullable=False,
        index=True,
    )

    external_user_id: Mapped[str] = mapped_column(String, nullable=False)
    external_org_id: Mapped[str] = mapped_column(String, nullable=True)

    access_token: Mapped[str] = mapped_column(String, nullable=False)
    refresh_token: Mapped[str] = mapped_column(String, nullable=False)

    instance_url: Mapped[str | None] = mapped_column(String, nullable=True)
    scope: Mapped[str | None] = mapped_column(String, nullable=True)
    token_type: Mapped[str | None] = mapped_column(String, nullable=True)

    expires_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))
    last_refreshed_at: Mapped[datetime | None] = mapped_column(DateTime(timezone=True))

    user: Mapped[User] = relationship(User)
    integration_config: Mapped[IntegrationConfig] = relationship(IntegrationConfig)

    __table_args__ = (
        UniqueConstraint(
            "user_id", "integration_config_id", name="uq_user_integration_config"
        ),
    )
