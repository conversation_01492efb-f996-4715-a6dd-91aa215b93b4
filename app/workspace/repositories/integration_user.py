import uuid

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.orm.base_async_repository import BaseAsyncRepository
from app.workspace.models import IntegrationUser


class IntegrationUserRepository(BaseAsyncRepository[IntegrationUser]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, IntegrationUser)

    async def get_by_user_and_integration(
        self, user_id: uuid.UUID, integration_config_id: uuid.UUID
    ) -> IntegrationUser | None:
        integration_users = await self._get_by_attrs(
            user_id=user_id, integration_config_id=integration_config_id
        )
        return integration_users[0] if integration_users else None

    async def get_by_external_user_and_integration(
        self, integration_config_id: uuid.UUID, external_user_id: str
    ) -> IntegrationUser | None:
        integration_users = await self._get_by_attrs(
            external_user_id=external_user_id,
            integration_config_id=integration_config_id,
        )
        return integration_users[0] if integration_users else None
