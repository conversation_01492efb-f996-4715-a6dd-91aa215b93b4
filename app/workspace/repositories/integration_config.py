from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.common.orm.base_async_repository import BaseAsyncRepository
from app.integrations.types import IntegrationSource
from app.workspace.models import Environment, IntegrationConfig, Organization
from app.workspace.types import EnvironmentType


class IntegrationConfigRepository(BaseAsyncRepository[IntegrationConfig]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, IntegrationConfig)

    async def get_all_by_org_id(
        self, org_id: UUID, env_type: EnvironmentType | None = None
    ) -> list[IntegrationConfig]:
        stmt = (
            select(IntegrationConfig)
            .join(IntegrationConfig.environment)
            .join(Environment.organization)
            .where(IntegrationConfig.is_active.is_(True), Organization.id == org_id)
        )

        if env_type is not None:
            stmt = stmt.where(Environment.type == env_type)

        result = await self.db_session.execute(stmt)

        return list(result.scalars().all())

    async def get_by_org_and_source(
        self,
        org_id: UUID,
        source: IntegrationSource,
        env_type: EnvironmentType | None = None,
    ) -> IntegrationConfig | None:
        configs = await self.get_all_by_org_id(org_id, env_type)
        for config in configs:
            if config.source == source:
                return config
        return None
