from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.common.orm.base_async_repository import BaseAsyncRepository
from app.workspace.models import Organization, OrganizationMember


class OrganizationRepository(BaseAsyncRepository[Organization]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, Organization)

    async def get_by_user_id(self, user_id: UUID) -> Organization | None:
        # Check if the user is a member of any organization
        stmt = (
            select(Organization)
            .join(
                OrganizationMember,
                Organization.id == OrganizationMember.organization_id,
            )
            .where(OrganizationMember.user_id == user_id)
        )

        result = await self.db_session.execute(stmt)
        org = result.scalars().first()

        if org:
            return org

        # If the user is not a member of any organization, check if they are the owner of any organization
        stmt = select(Organization).where(Organization.owner_id == user_id)
        result = await self.db_session.execute(stmt)

        return result.scalars().first()
