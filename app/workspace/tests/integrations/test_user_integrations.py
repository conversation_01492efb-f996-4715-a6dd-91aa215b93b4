from datetime import datetime
from typing import Any
from unittest.mock import AsyncMock
from uuid import uuid4

import pytest

from app.integrations.factory import IntegrationFactory
from app.workspace.integrations.user_integrations import UserIntegrations
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.salesforce_connection import SalesforceConnectionService
from app.workspace.types import EnvironmentType, IntegrationType


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid4(),
        organization_id=uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def mocks(mocker, mock_environment) -> dict[str, Any]:
    integration_config = mocker.MagicMock()
    integration_config.id = uuid4()
    integration_config.source = "salesforce"

    integration_user = mocker.MagicMock()
    integration_user.external_user_id = "crm_user_123"
    integration_user.external_org_id = "crm_org_456"

    integration_cfg_service = mocker.AsyncMock(spec=IntegrationConfigService)
    integration_cfg_service.get_crm_config = AsyncMock(return_value=integration_config)
    integration_cfg_service.get_integration_user = AsyncMock(
        return_value=integration_user
    )

    salesforce_connection_service = mocker.MagicMock(spec=SalesforceConnectionService)

    crm_provider = mocker.AsyncMock()
    mock_integration_factory = mocker.MagicMock(spec=IntegrationFactory)
    mock_integration_factory.crm.return_value = crm_provider

    mock_create_factory = mocker.patch(
        "app.workspace.integrations.user_integrations.create_factory",
        return_value=mock_integration_factory,
    )

    return {
        "environment": mock_environment,
        "integration_config": integration_config,
        "integration_user": integration_user,
        "integration_cfg_service": integration_cfg_service,
        "salesforce_connection_service": salesforce_connection_service,
        "crm_provider": crm_provider,
        "integration_factory": mock_integration_factory,
        "create_factory_mock": mock_create_factory,
    }


@pytest.fixture
def integrations_params() -> dict[str, Any]:
    return {
        "user_id": uuid4(),
    }


def test_init_integrations(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    assert integrations.user_id == integrations_params["user_id"]
    assert integrations.environment == mocks["environment"]
    assert integrations.org_id == mocks["environment"].organization_id
    assert integrations.env_type == mocks["environment"].type
    assert integrations.db_session == db_session
    assert integrations.integration_cfg_service == mocks["integration_cfg_service"]
    assert (
        integrations.salesforce_connection_service
        == mocks["salesforce_connection_service"]
    )
    assert integrations._factory == mocks["integration_factory"]

    mocks["create_factory_mock"].assert_called_once()

    assert len(integrations._handles) == 0
    assert len(integrations._configs) == 0
    assert len(integrations._integration_users) == 0


@pytest.mark.anyio
async def test_crm_provider_initialization(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    provider = await integrations.crm()

    mocks["integration_cfg_service"].get_crm_config.assert_called_once_with(
        environment=mocks["environment"]
    )

    mocks["integration_cfg_service"].get_integration_user.assert_called_once_with(
        integration_config_id=mocks["integration_config"].id,
        user_id=integrations_params["user_id"],
    )

    mocks["integration_factory"].crm.assert_called_once_with(
        mocks["integration_config"].source
    )

    assert integrations._handles[IntegrationType.CRM] == mocks["crm_provider"]
    assert provider == mocks["crm_provider"]

    await integrations.crm()
    mocks["integration_cfg_service"].get_crm_config.assert_called_once()
    mocks["integration_cfg_service"].get_integration_user.assert_called_once()
    mocks["integration_factory"].crm.assert_called_once()


@pytest.mark.anyio
async def test_crm_user_id_property(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    user_id = await integrations.crm_user_id()

    assert user_id == mocks["integration_user"].external_user_id
    assert (
        integrations._integration_users[IntegrationType.CRM]
        == mocks["integration_user"]
    )

    second_user_id = await integrations.crm_user_id()
    assert second_user_id == user_id
    mocks["integration_cfg_service"].get_integration_user.assert_called_once()


@pytest.mark.anyio
async def test_crm_org_id_property(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    org_id = await integrations.crm_org_id()

    assert org_id == mocks["integration_user"].external_org_id
    assert (
        integrations._integration_users[IntegrationType.CRM]
        == mocks["integration_user"]
    )


@pytest.mark.anyio
async def test_no_active_crm_integration(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_crm_config = AsyncMock(return_value=None)

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    result = await integrations.crm()
    assert result is None


@pytest.mark.anyio
async def test_get_crm_accounts(
    mocker,
    mocks,
    integrations_params,
    db_session,
) -> None:
    mock_accounts = [
        {"id": "acc_1", "name": "Account 1"},
        {"id": "acc_2", "name": "Account 2"},
    ]

    crm_provider = mocks["crm_provider"]
    crm_provider.list_account_access = mocker.AsyncMock(return_value=mock_accounts)

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    result = await integrations.get_crm_accounts()

    crm_provider.list_account_access.assert_called_once_with(
        mocks["integration_user"].external_user_id
    )

    assert result == mock_accounts


@pytest.mark.anyio
async def test_get_crm_accounts_no_crm(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_crm_config = AsyncMock(return_value=None)

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    result = await integrations.get_crm_accounts()

    assert result == []


@pytest.mark.anyio
async def test_get_crm_accounts_no_user_id(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_integration_user = AsyncMock(return_value=None)

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    result = await integrations.get_crm_accounts()

    assert result == []


@pytest.mark.anyio
async def test_sync_crm_accounts(
    mocks,
    integrations_params,
    db_session,
):
    crm_provider = mocks["crm_provider"]

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    await integrations.sync_crm_accounts()

    crm_provider.bulk_sync_account_access.assert_called_once_with(
        crm_user_ids=[mocks["integration_user"].external_user_id]
    )


@pytest.mark.anyio
async def test_sync_crm_accounts_no_crm(
    mocks,
    integrations_params,
    db_session,
):
    mocks["integration_cfg_service"].get_crm_config = AsyncMock(return_value=None)

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    await integrations.sync_crm_accounts()

    mocks["integration_factory"].crm.assert_not_called()


@pytest.mark.anyio
async def test_no_integration_user_for_user(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_integration_user = AsyncMock(return_value=None)

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    result = await integrations.crm()
    assert result is None


@pytest.mark.anyio
async def test_caching_behavior(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    config = await integrations._get_config(IntegrationType.CRM)
    assert config == mocks["integration_config"]
    mocks["integration_cfg_service"].get_crm_config.assert_called_once()

    config = await integrations._get_config(IntegrationType.CRM)
    assert config == mocks["integration_config"]
    mocks["integration_cfg_service"].get_crm_config.assert_called_once()

    integration_user = await integrations._get_integration_user(IntegrationType.CRM)
    assert integration_user == mocks["integration_user"]
    mocks["integration_cfg_service"].get_integration_user.assert_called_once()

    integration_user = await integrations._get_integration_user(IntegrationType.CRM)
    assert integration_user == mocks["integration_user"]
    mocks["integration_cfg_service"].get_integration_user.assert_called_once()


@pytest.mark.anyio
async def test_provider_factory_error(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_factory"].crm.side_effect = Exception("Provider factory error")

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    result = await integrations.crm()
    assert result is None


@pytest.mark.anyio
async def test_unsupported_integration_type(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    result = await integrations._get_handle(IntegrationType.MESSAGING)
    assert result is None


@pytest.mark.anyio
async def test_none_config_not_cached(
    mocks,
    integrations_params,
    db_session,
) -> None:
    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    config = await integrations._get_config(IntegrationType.MESSAGING)
    assert config is None

    assert IntegrationType.MESSAGING not in integrations._configs


@pytest.mark.anyio
async def test_none_integration_user_not_cached(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_integration_user = AsyncMock(return_value=None)

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    integration_user = await integrations._get_integration_user(IntegrationType.CRM)
    assert integration_user is None

    assert IntegrationType.CRM not in integrations._integration_users


@pytest.mark.anyio
async def test_none_provider_not_cached(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_integration_user = AsyncMock(return_value=None)

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    provider = await integrations.crm()
    assert provider is None

    assert IntegrationType.CRM not in integrations._handles


@pytest.mark.anyio
async def test_config_dynamically_available(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_crm_config = AsyncMock(return_value=None)

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    result = await integrations.crm()
    assert result is None
    assert IntegrationType.CRM not in integrations._configs

    mocks["integration_cfg_service"].get_crm_config = AsyncMock(
        return_value=mocks["integration_config"]
    )

    result = await integrations.crm()
    assert result == mocks["crm_provider"]
    assert integrations._configs[IntegrationType.CRM] == mocks["integration_config"]


@pytest.mark.anyio
async def test_file_provider_initialization(
    mocker,
    mocks,
    integrations_params,
    db_session,
) -> None:
    file_integration_config = mocks["integration_config"]
    mocks["integration_cfg_service"].get_file_config = AsyncMock(
        return_value=file_integration_config
    )

    file_provider = mocker.AsyncMock()
    mocks["integration_factory"].file.return_value = file_provider

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    provider = await integrations.file()

    mocks["integration_cfg_service"].get_file_config.assert_called_once_with(
        environment=mocks["environment"]
    )

    mocks["integration_factory"].file.assert_called_once_with(
        file_integration_config.source
    )

    assert integrations._handles[IntegrationType.FILE] == file_provider
    assert provider == file_provider

    await integrations.file()
    mocks["integration_cfg_service"].get_file_config.assert_called_once()
    mocks["integration_factory"].file.assert_called_once()


@pytest.mark.anyio
async def test_search_files_success(
    mocker,
    mocks,
    integrations_params,
    db_session,
) -> None:
    file_integration_config = mocks["integration_config"]
    mocks["integration_cfg_service"].get_file_config = AsyncMock(
        return_value=file_integration_config
    )

    file_provider = mocker.AsyncMock()
    mock_search_results = [
        (mocker.Mock(content="Document 1: Sales contract terms"), 0.95),
        (mocker.Mock(content="Document 2: Payment schedule"), 0.87),
        (mocker.Mock(content="Document 3: Invoice template"), 0.76),
    ]
    file_provider.search_files = AsyncMock(return_value=mock_search_results)
    mocks["integration_factory"].file.return_value = file_provider

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    query = "payment terms contract"
    results = await integrations.search_files(query, limit=5)

    file_provider.search_files.assert_called_once_with(query, 5)
    assert results == [
        "Document 1: Sales contract terms",
        "Document 2: Payment schedule",
        "Document 3: Invoice template",
    ]


@pytest.mark.anyio
async def test_search_files_no_file_integration(
    mocks,
    integrations_params,
    db_session,
) -> None:
    mocks["integration_cfg_service"].get_file_config = AsyncMock(return_value=None)

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    results = await integrations.search_files("test query")

    assert results == []
    mocks["integration_factory"].file.assert_not_called()


@pytest.mark.anyio
async def test_search_files_empty_results(
    mocker,
    mocks,
    integrations_params,
    db_session,
) -> None:
    file_integration_config = mocks["integration_config"]
    mocks["integration_cfg_service"].get_file_config = AsyncMock(
        return_value=file_integration_config
    )

    file_provider = mocker.AsyncMock()
    file_provider.search_files = AsyncMock(return_value=[])
    mocks["integration_factory"].file.return_value = file_provider

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    results = await integrations.search_files("test query")

    assert results == []


@pytest.mark.anyio
async def test_search_files_provider_error(
    mocks,
    integrations_params,
    db_session,
) -> None:
    file_integration_config = mocks["integration_config"]
    mocks["integration_cfg_service"].get_file_config = AsyncMock(
        return_value=file_integration_config
    )

    mocks["integration_factory"].file.side_effect = Exception(
        "Provider initialization failed"
    )

    integrations = UserIntegrations(
        environment=mocks["environment"],
        integration_cfg_service=mocks["integration_cfg_service"],
        salesforce_connection_service=mocks["salesforce_connection_service"],
        db_session=db_session,
        **integrations_params,
    )

    results = await integrations.search_files("test query")

    assert results == []
