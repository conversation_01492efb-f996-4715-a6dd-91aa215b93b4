import uuid

import pytest

from app.auth.models import User
from app.workspace.models import (
    Organization,
    OrganizationMember,
)
from app.workspace.repositories.organization import OrganizationRepository


async def setup_user_and_orgs(async_db_session):
    user1_id = uuid.uuid4()
    user2_id = uuid.uuid4()
    user3_id = uuid.uuid4()
    org1_id = uuid.uuid4()
    org2_id = uuid.uuid4()

    user1 = User(
        id=user1_id,
        email="<EMAIL>",
        first_name="User",
        last_name="One",
    )
    user2 = User(
        id=user2_id,
        email="<EMAIL>",
        first_name="User",
        last_name="Two",
    )
    user3 = User(
        id=user3_id,
        email="<EMAIL>",
        first_name="User",
        last_name="Three",
    )
    async_db_session.add_all([user1, user2, user3])
    await async_db_session.flush()

    org1 = Organization(
        id=org1_id,
        name="Org 1",
        domain="org1.com",
        is_active=True,
        owner_id=user1_id,
    )
    org2 = Organization(
        id=org2_id,
        name="Org 2",
        domain="org2.com",
        is_active=True,
        owner_id=user2_id,
    )
    async_db_session.add_all([org1, org2])
    await async_db_session.flush()

    member = OrganizationMember(
        user_id=user3_id,
        organization_id=org1_id,
    )
    async_db_session.add(member)
    await async_db_session.commit()

    return {
        "user1_id": user1_id,
        "user2_id": user2_id,
        "user3_id": user3_id,
        "org1_id": org1_id,
        "org2_id": org2_id,
    }


@pytest.mark.anyio
async def test_get_by_user_id_as_owner(async_db_session):
    setup = await setup_user_and_orgs(async_db_session)
    repo = OrganizationRepository(async_db_session)

    result = await repo.get_by_user_id(setup["user1_id"])
    assert result is not None
    assert result.id == setup["org1_id"]
    assert result.name == "Org 1"


@pytest.mark.anyio
async def test_get_by_user_id_as_member(async_db_session):
    setup = await setup_user_and_orgs(async_db_session)
    repo = OrganizationRepository(async_db_session)

    result = await repo.get_by_user_id(setup["user3_id"])
    assert result is not None
    assert result.id == setup["org1_id"]
    assert result.name == "Org 1"


@pytest.mark.anyio
async def test_get_by_user_id_with_no_org(async_db_session):
    user_id = uuid.uuid4()
    user = User(
        id=user_id,
        email="<EMAIL>",
        first_name="No",
        last_name="Organization",
    )
    async_db_session.add(user)
    await async_db_session.commit()

    repo = OrganizationRepository(async_db_session)

    result = await repo.get_by_user_id(user_id)
    assert result is None
