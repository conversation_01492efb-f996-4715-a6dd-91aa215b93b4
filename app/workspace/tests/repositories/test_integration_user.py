# ruff: noqa: S106

import uuid

import pytest

from app.auth.models import User
from app.integrations.types import IntegrationSource
from app.workspace.models import (
    Environment,
    IntegrationConfig,
    IntegrationUser,
    Organization,
)
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.types import EnvironmentType, IntegrationType


async def create_integration_user_setup(async_db_session):
    user1_id = uuid.uuid4()
    user2_id = uuid.uuid4()
    organization_id = uuid.uuid4()
    environment_id = uuid.uuid4()
    integration_config1_id = uuid.uuid4()
    integration_config2_id = uuid.uuid4()

    user1 = User(
        id=user1_id,
        email="<EMAIL>",
        first_name="<PERSON>",
        last_name="<PERSON><PERSON>",
    )
    user2 = User(
        id=user2_id,
        email="<EMAIL>",
        first_name="<PERSON>",
        last_name="<PERSON>",
    )
    organization = Organization(
        id=organization_id,
        name="Test Organization",
        domain="test.org",
        owner_id=user1_id,
    )
    environment = Environment(
        id=environment_id,
        organization_id=organization_id,
        type=EnvironmentType.PROD,
    )
    integration_config1 = IntegrationConfig(
        id=integration_config1_id,
        environment_id=environment_id,
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
    )
    integration_config2 = IntegrationConfig(
        id=integration_config2_id,
        environment_id=environment_id,
        source=IntegrationSource.SLACK,
        integration_type=IntegrationType.MESSAGING,
    )
    async_db_session.add_all(
        [
            user1,
            user2,
            organization,
            environment,
            integration_config1,
            integration_config2,
        ]
    )
    await async_db_session.flush()

    return {
        "user1_id": user1_id,
        "user2_id": user2_id,
        "integration_config1_id": integration_config1_id,
        "integration_config2_id": integration_config2_id,
    }


@pytest.mark.anyio
async def test_get_by_user_and_integration(async_db_session):
    setup = await create_integration_user_setup(async_db_session)
    repo = IntegrationUserRepository(async_db_session)

    token1_id = uuid.uuid4()
    token2_id = uuid.uuid4()
    token3_id = uuid.uuid4()

    token1 = IntegrationUser(
        id=token1_id,
        user_id=setup["user1_id"],
        integration_config_id=setup["integration_config1_id"],
        external_user_id="ext_user_1",
        external_org_id="ext_org_1",
        access_token="access_token_1",
        refresh_token="refresh_token_1",
    )
    token2 = IntegrationUser(
        id=token2_id,
        user_id=setup["user1_id"],
        integration_config_id=setup["integration_config2_id"],
        external_user_id="ext_user_2",
        external_org_id="ext_org_2",
        access_token="access_token_2",
        refresh_token="refresh_token_2",
    )
    token3 = IntegrationUser(
        id=token3_id,
        user_id=setup["user2_id"],
        integration_config_id=setup["integration_config1_id"],
        external_user_id="ext_user_3",
        external_org_id="ext_org_3",
        access_token="access_token_3",
        refresh_token="refresh_token_3",
    )

    async_db_session.add_all([token1, token2, token3])
    await async_db_session.flush()

    found_token = await repo.get_by_user_and_integration(
        setup["user1_id"], setup["integration_config1_id"]
    )
    assert found_token is not None
    assert found_token.external_user_id == "ext_user_1"
    assert found_token.access_token == "access_token_1"

    found_token = await repo.get_by_user_and_integration(
        setup["user2_id"], setup["integration_config1_id"]
    )
    assert found_token is not None
    assert found_token.external_user_id == "ext_user_3"
    assert found_token.access_token == "access_token_3"

    found_token = await repo.get_by_user_and_integration(
        setup["user2_id"], setup["integration_config2_id"]
    )
    assert found_token is None


@pytest.mark.anyio
async def test_get_by_external_user_and_integration(async_db_session):
    setup = await create_integration_user_setup(async_db_session)
    repo = IntegrationUserRepository(async_db_session)

    token1_id = uuid.uuid4()
    token2_id = uuid.uuid4()
    token3_id = uuid.uuid4()
    token4_id = uuid.uuid4()

    token1 = IntegrationUser(
        id=token1_id,
        user_id=setup["user1_id"],
        integration_config_id=setup["integration_config1_id"],
        external_user_id="ext_user_1",
        external_org_id="ext_org_1",
        access_token="access_token_1",
        refresh_token="refresh_token_1",
    )
    token2 = IntegrationUser(
        id=token2_id,
        user_id=setup["user1_id"],
        integration_config_id=setup["integration_config2_id"],
        external_user_id="ext_user_2",
        external_org_id="ext_org_2",
        access_token="access_token_2",
        refresh_token="refresh_token_2",
    )
    token3 = IntegrationUser(
        id=token3_id,
        user_id=setup["user2_id"],
        integration_config_id=setup["integration_config1_id"],
        external_user_id="ext_user_3",
        external_org_id="ext_org_3",
        access_token="access_token_3",
        refresh_token="refresh_token_3",
    )
    token4 = IntegrationUser(
        id=token4_id,
        user_id=setup["user2_id"],
        integration_config_id=setup["integration_config2_id"],
        external_user_id="ext_user_1",
        external_org_id="ext_org_4",
        access_token="access_token_4",
        refresh_token="refresh_token_4",
    )

    async_db_session.add_all([token1, token2, token3, token4])
    await async_db_session.flush()

    found_token = await repo.get_by_external_user_and_integration(
        setup["integration_config1_id"], "ext_user_1"
    )
    assert found_token is not None
    assert found_token.user_id == setup["user1_id"]
    assert found_token.access_token == "access_token_1"

    found_token = await repo.get_by_external_user_and_integration(
        setup["integration_config1_id"], "ext_user_3"
    )
    assert found_token is not None
    assert found_token.user_id == setup["user2_id"]
    assert found_token.access_token == "access_token_3"

    found_token = await repo.get_by_external_user_and_integration(
        setup["integration_config2_id"], "ext_user_1"
    )
    assert found_token is not None
    assert found_token.user_id == setup["user2_id"]
    assert found_token.access_token == "access_token_4"

    found_token = await repo.get_by_external_user_and_integration(
        setup["integration_config1_id"], "nonexistent_user"
    )
    assert found_token is None

    found_token = await repo.get_by_external_user_and_integration(
        setup["integration_config2_id"], "ext_user_3"
    )
    assert found_token is None
