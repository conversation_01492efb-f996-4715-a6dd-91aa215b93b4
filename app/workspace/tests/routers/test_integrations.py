import pytest

from app.integrations.types import IntegrationSource
from app.main import app
from app.workspace.dependencies import get_integration_list_service
from app.workspace.schemas.integration import IntegrationInfo, IntegrationsListResponse
from app.workspace.services.integration_list import IntegrationListService
from app.workspace.types import IntegrationType


@pytest.fixture
def override_integration_list_service(mocker):
    mock_service = mocker.AsyncMock(spec=IntegrationListService)
    app.dependency_overrides[get_integration_list_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_integration_list_service)


@pytest.mark.anyio
async def test_get_integrations_endpoint(
    async_client, test_app, override_integration_list_service
):
    active_integrations = [
        IntegrationInfo(
            id="salesforce",
            source=IntegrationSource.SALESFORCE,
            integration_type=IntegrationType.CRM,
            name="Salesforce",
            description="Connect your Salesforce CRM to sync leads, contacts, and opportunities",
            is_active=True,
        ),
    ]

    available_integrations = [
        IntegrationInfo(
            id="google-calendar",
            source=IntegrationSource.GOOGLE_CALENDAR,
            integration_type=IntegrationType.CALENDAR,
            name="Google Calendar",
            description="Sync your Google Calendar events and meetings",
            is_active=False,
        ),
    ]

    mock_response = IntegrationsListResponse(
        active_integrations=active_integrations,
        available_integrations=available_integrations,
    )

    override_integration_list_service.get_integrations_list.return_value = mock_response

    url = test_app.url_path_for("get_integrations")
    response = await async_client.get(url)

    assert response.status_code == 200
    data = response.json()

    assert "active_integrations" in data
    assert "available_integrations" in data

    assert len(data["active_integrations"]) == 1
    assert len(data["available_integrations"]) == 1

    active = data["active_integrations"]
    assert active[0]["source"] == "salesforce"
    assert active[0]["is_active"] is True
    assert active[0]["id"] is not None

    available = data["available_integrations"]
    assert available[0]["source"] == "google_calendar"
    assert available[0]["is_active"] is False
    assert available[0]["id"] is not None

    override_integration_list_service.get_integrations_list.assert_called_once()
