from unittest.mock import AsyncMock

import pytest

from app.main import app
from app.workspace.dependencies import get_calendar_service


@pytest.fixture
def mock_calendar_service():
    mock_service = AsyncMock()

    mock_service.list_calendars.return_value = [
        {"id": "primary", "name": "Primary Calendar", "is_primary": True},
        {"id": "calendar2", "name": "Secondary Calendar", "is_primary": False},
    ]

    mock_service.get_calendar.return_value = {
        "id": "primary",
        "name": "Primary Calendar",
        "description": "Main calendar",
        "is_primary": True,
    }

    mock_service.list_events.return_value = {
        "events": [
            {
                "id": "event1",
                "title": "Meeting 1",
                "start": {"dateTime": "2024-01-01T10:00:00Z"},
                "end": {"dateTime": "2024-01-01T11:00:00Z"},
            },
            {
                "id": "event2",
                "title": "Meeting 2",
                "start": {"dateTime": "2024-01-01T14:00:00Z"},
                "end": {"dateTime": "2024-01-01T15:00:00Z"},
            },
        ],
        "next_page_token": None,
    }

    mock_service.get_event.return_value = {
        "id": "event1",
        "title": "Meeting 1",
        "description": "Important meeting",
        "start": {"dateTime": "2024-01-01T10:00:00Z"},
        "end": {"dateTime": "2024-01-01T11:00:00Z"},
        "location": "Conference Room A",
    }

    return mock_service


@pytest.fixture
def override_calendar_service(mock_calendar_service):
    app.dependency_overrides[get_calendar_service] = lambda: mock_calendar_service
    yield mock_calendar_service
    app.dependency_overrides.pop(get_calendar_service)


class TestCalendarRouter:
    @pytest.mark.anyio
    async def test_list_calendars(
        self, async_client, test_app, override_calendar_service
    ):
        url = test_app.url_path_for("list_calendars")
        response = await async_client.get(url)

        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["id"] == "primary"
        assert data[0]["name"] == "Primary Calendar"
        assert data[0]["is_primary"] is True
        assert data[1]["id"] == "calendar2"
        assert data[1]["name"] == "Secondary Calendar"
        assert data[1]["is_primary"] is False

        override_calendar_service.list_calendars.assert_called_once()

    @pytest.mark.anyio
    async def test_get_calendar(
        self, async_client, test_app, override_calendar_service
    ):
        calendar_id = "primary"
        url = test_app.url_path_for("get_calendar", calendar_id=calendar_id)
        response = await async_client.get(url)

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "primary"
        assert data["name"] == "Primary Calendar"
        assert data["description"] == "Main calendar"
        assert data["is_primary"] is True

        override_calendar_service.get_calendar.assert_called_once_with(calendar_id)

    @pytest.mark.anyio
    async def test_list_events(self, async_client, test_app, override_calendar_service):
        calendar_id = "primary"
        url = test_app.url_path_for("list_events", calendar_id=calendar_id)
        response = await async_client.get(url)

        assert response.status_code == 200
        data = response.json()
        assert "events" in data
        assert "next_page_token" in data
        assert len(data["events"]) == 2
        assert data["events"][0]["id"] == "event1"
        assert data["events"][0]["title"] == "Meeting 1"
        assert data["events"][1]["id"] == "event2"
        assert data["events"][1]["title"] == "Meeting 2"

        override_calendar_service.list_events.assert_called_once_with(
            calendar_id, None, None
        )

    @pytest.mark.anyio
    async def test_list_events_with_date_filters(
        self, async_client, test_app, override_calendar_service
    ):
        calendar_id = "primary"
        start_date = "2024-01-01T00:00:00"
        end_date = "2024-01-31T23:59:59"

        url = test_app.url_path_for("list_events", calendar_id=calendar_id)
        response = await async_client.get(
            url,
            params={
                "start_date": start_date,
                "end_date": end_date,
            },
        )

        assert response.status_code == 200
        data = response.json()
        assert "events" in data
        assert len(data["events"]) == 2

        override_calendar_service.list_events.assert_called_once()
        call_args = override_calendar_service.list_events.call_args
        assert call_args[0][0] == calendar_id
        assert call_args[0][1] is not None
        assert call_args[0][2] is not None

    @pytest.mark.anyio
    async def test_get_event(self, async_client, test_app, override_calendar_service):
        calendar_id = "primary"
        event_id = "event1"
        url = test_app.url_path_for(
            "get_event", calendar_id=calendar_id, event_id=event_id
        )
        response = await async_client.get(url)

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == "event1"
        assert data["title"] == "Meeting 1"
        assert data["description"] == "Important meeting"
        assert data["location"] == "Conference Room A"
        assert "start" in data
        assert "end" in data

        override_calendar_service.get_event.assert_called_once_with(
            calendar_id, event_id
        )

    @pytest.mark.anyio
    async def test_list_events_with_invalid_date_format(self, async_client, test_app):
        calendar_id = "primary"
        url = test_app.url_path_for("list_events", calendar_id=calendar_id)

        response = await async_client.get(
            url,
            params={
                "start_date": "invalid-date",
                "end_date": "2024-01-31T23:59:59",
            },
        )

        assert response.status_code == 422

    @pytest.mark.anyio
    async def test_calendar_service_dependency_injection(
        self, async_client, test_app, override_calendar_service
    ):
        url = test_app.url_path_for("list_calendars")
        response = await async_client.get(url)
        assert response.status_code == 200

        url = test_app.url_path_for("get_calendar", calendar_id="primary")
        response = await async_client.get(url)
        assert response.status_code == 200

        url = test_app.url_path_for("list_events", calendar_id="primary")
        response = await async_client.get(url)
        assert response.status_code == 200

        url = test_app.url_path_for(
            "get_event", calendar_id="primary", event_id="event1"
        )
        response = await async_client.get(url)
        assert response.status_code == 200

        assert override_calendar_service.list_calendars.call_count == 1
        assert override_calendar_service.get_calendar.call_count == 1
        assert override_calendar_service.list_events.call_count == 1
        assert override_calendar_service.get_event.call_count == 1
