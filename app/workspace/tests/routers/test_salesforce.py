from uuid import UUID

import pytest

from app.main import app
from app.workspace.dependencies import get_salesforce_connection_service, get_user_env
from app.workspace.types import EnvironmentType


@pytest.fixture
def override_user_env(mocker):
    mock_env = mocker.Mock()
    mock_env.id = UUID("*************-2222-2222-************")
    mock_env.organization_id = UUID("*************-3333-3333-************")
    mock_env.type = EnvironmentType.PROD
    app.dependency_overrides[get_user_env] = lambda: mock_env
    yield mock_env
    app.dependency_overrides.pop(get_user_env)


@pytest.fixture
def override_salesforce_service_auth_url(mocker):
    mock_service = mocker.Mock()
    mock_service.generate_oauth_authorization_uri = mocker.AsyncMock(
        return_value="https://salesforce.com/oauth/authorize?test=true"
    )
    app.dependency_overrides[get_salesforce_connection_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_salesforce_connection_service)


@pytest.fixture
def override_salesforce_service_callback(mocker):
    class MockToken:
        @property
        def external_user_id(self):
            return "sf_user_123"

        @property
        def access_token(self):
            return "mock_access_token"

    mock_service = mocker.Mock()
    mock_service.process_oauth_callback = mocker.AsyncMock(return_value=MockToken())

    app.dependency_overrides[get_salesforce_connection_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_salesforce_connection_service)


@pytest.mark.anyio
async def test_get_salesforce_auth_url(
    async_client, test_app, override_salesforce_service_auth_url, override_user_env
):
    url = test_app.url_path_for("get_salesforce_auth_url")
    response = await async_client.get(url)

    assert response.status_code == 200, response.text
    data = response.json()
    assert "auth_url" in data
    assert data["auth_url"] == "https://salesforce.com/oauth/authorize?test=true"

    override_salesforce_service_auth_url.generate_oauth_authorization_uri.assert_called_once()

    call_args = (
        override_salesforce_service_auth_url.generate_oauth_authorization_uri.call_args[
            1
        ]
    )
    assert call_args["environment"] == override_user_env
    assert call_args["user_id"] == "mocked_user_id"


@pytest.mark.anyio
async def test_process_salesforce_callback(
    async_client, test_app, override_salesforce_service_callback, override_user_env
):
    url = test_app.url_path_for("process_salesforce_callback")
    response = await async_client.get(f"{url}?code=test_code&state=test_state")

    assert response.status_code == 200, response.text
    data = response.json()
    assert "message" in data
    assert data["message"] == "OAuth succeed"
    assert data["crm_user_id"] == "sf_user_123"
    assert data["user_id"] == "mocked_user_id"

    assert override_salesforce_service_callback.process_oauth_callback.called
    assert override_user_env.id == UUID("*************-2222-2222-************")
