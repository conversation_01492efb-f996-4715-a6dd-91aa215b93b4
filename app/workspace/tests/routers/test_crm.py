from unittest.mock import AsyncMock

import pytest

from app.main import app
from app.workspace.dependencies import CRMServiceDep, get_crm_service
from app.workspace.schemas import AccountRead, UserCRMInfo


@pytest.fixture
def override_crm_service_success(mocker):
    mock_service = mocker.AsyncMock()
    mock_service.get_crm = AsyncMock(
        return_value=UserCRMInfo(crm_name="salesforce", crm_user_id="crm_user_id1")
    )

    account_1 = AccountRead(
        crm_id="1",
        crm_name="Account1",
    )
    account_2 = AccountRead(
        crm_id="2",
        crm_name="Account2",
    )

    mock_service.get_accounts = AsyncMock(return_value=[account_1, account_2])
    mock_service.sync_accounts = AsyncMock(return_value=None)

    app.dependency_overrides[get_crm_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_crm_service)


@pytest.fixture
def override_crm_service_none(mocker):
    mock_service = mocker.AsyncMock()
    mock_service.get_crm = AsyncMock(return_value=None)
    app.dependency_overrides[get_crm_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_crm_service)


@pytest.mark.anyio
async def test_get_user_crm_success(
    async_client, test_app, override_crm_service_success
):
    url = test_app.url_path_for("get_user_crm")
    response = await async_client.get(url)
    assert response.status_code == 200, response.text
    data = response.json()
    assert isinstance(data, dict)
    assert data["crm_name"] == "salesforce"
    assert data["crm_user_id"] == "crm_user_id1"
    override_crm_service_success.get_crm.assert_called_once()


@pytest.mark.anyio
async def test_get_user_crm_none(async_client, test_app, override_crm_service_none):
    url = test_app.url_path_for("get_user_crm")
    response = await async_client.get(url)
    assert response.status_code == 200, response.text
    data = response.json()
    assert data is None
    override_crm_service_none.get_crm.assert_called_once()


@pytest.mark.anyio
async def test_get_accounts_success(
    async_client, test_app, override_crm_service_success
):
    url = test_app.url_path_for("get_accounts")
    response = await async_client.get(url)
    assert response.status_code == 200, response.text
    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 2
    names = [account["crm_name"] for account in data]
    assert "Account1" in names
    assert "Account2" in names
    override_crm_service_success.get_accounts.assert_called_once()


@pytest.mark.anyio
async def test_sync_accounts_success(
    async_client, test_app, override_crm_service_success
):
    url = test_app.url_path_for("sync_accounts")
    response = await async_client.get(url)
    assert response.status_code == 200, response.text
    override_crm_service_success.sync_accounts.assert_called_once()


@pytest.mark.anyio
async def test_sync_accounts_endpoint_error(async_client, test_app, mocker):
    url = test_app.url_path_for("get_accounts")
    service_mock = mocker.Mock()
    service_mock.sync_accounts.side_effect = Exception
    app.dependency_overrides[CRMServiceDep] = lambda: service_mock
    response = await async_client.get(url)
    assert response.status_code == 500
    app.dependency_overrides.pop(CRMServiceDep)
