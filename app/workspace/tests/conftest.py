import pytest
from pytest_mock import MockerFixture

from app.auth.dependencies import get_authenticated_user_id
from app.main import app
from app.workspace.dependencies import (
    get_crm_service,
    get_organization_service,
    get_organization_team_service,
)
from app.workspace.schemas import AccountRead
from app.workspace.services.crm import CRMService
from app.workspace.services.organization import OrganizationService
from app.workspace.services.organization_team import OrganizationTeamService


@pytest.fixture(autouse=True)
def override_authenticated_user_id():
    def mock_get_authenticated_user_id():
        return "mocked_user_id"

    app.dependency_overrides[get_authenticated_user_id] = mock_get_authenticated_user_id
    yield
    app.dependency_overrides.pop(get_authenticated_user_id)


@pytest.fixture
def override_crm_service_success(mocker):
    mock_service = mocker.AsyncMock(spec=CRMService)

    account_1 = AccountRead(
        crm_id="1",
        crm_name="Account1",
    )
    account_2 = AccountRead(
        crm_id="2",
        crm_name="Account2",
    )

    mock_service.get_accounts = mocker.AsyncMock(return_value=[account_1, account_2])
    mock_service.sync_accounts = mocker.AsyncMock(return_value=None)

    app.dependency_overrides[get_crm_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_crm_service)


@pytest.fixture
def override_organization_team_service(mocker: MockerFixture):
    mock_service = mocker.AsyncMock(spec=OrganizationTeamService)
    app.dependency_overrides[get_organization_team_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_organization_team_service)


@pytest.fixture(autouse=True)
def override_organization_service(mocker: MockerFixture):
    mock_service = mocker.AsyncMock(spec=OrganizationService)
    app.dependency_overrides[get_organization_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_organization_service)
