import uuid

import pytest
from sqlalchemy.exc import IntegrityError

from app.auth.models import User
from app.workspace.models import OrganizationMember
from app.workspace.models.organization import Organization


@pytest.fixture
def org_and_user(db_session):
    user = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
    )
    db_session.add(user)
    db_session.commit()

    owner = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="Admin",
        last_name="User",
    )
    db_session.add(owner)
    db_session.commit()

    organization = Organization(
        id=uuid.uuid4(),
        name="Test Organization",
        domain="test.org",
        owner_id=owner.id,
    )
    db_session.add(organization)
    db_session.commit()

    return organization, user


def test_create_organization_member(db_session, org_and_user):
    organization, user = org_and_user

    member = OrganizationMember(
        organization_id=organization.id,
        user_id=user.id,
    )
    db_session.add(member)
    db_session.commit()

    result = db_session.query(OrganizationMember).filter_by(id=member.id).first()
    assert result is not None
    assert result.user == user
    assert result.organization == organization


def test_unique_user_constraint(db_session, org_and_user):
    organization, user = org_and_user

    member1 = OrganizationMember(
        organization_id=organization.id,
        user_id=user.id,
    )
    db_session.add(member1)
    db_session.commit()

    member2 = OrganizationMember(
        organization_id=organization.id,
        user_id=user.id,
    )
    db_session.add(member2)

    with pytest.raises(IntegrityError):
        db_session.commit()
