import uuid

import pytest
from sqlalchemy.exc import IntegrityError

from app.auth.models import User
from app.integrations.types import IntegrationSource
from app.workspace.models import Environment
from app.workspace.models.integration_config import (
    IntegrationConfig,
)
from app.workspace.models.organization import Organization
from app.workspace.types import EnvironmentType, IntegrationType


@pytest.fixture
def user_org_env(db_session):
    user = User(
        id=uuid.uuid4(),
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
    )
    db_session.add(user)
    db_session.commit()

    org = Organization(
        id=uuid.uuid4(),
        name="Test Organization",
        domain="test.org",
        owner_id=user.id,
    )
    db_session.add(org)
    db_session.commit()

    env = Environment(
        id=uuid.uuid4(),
        type=EnvironmentType.SANDBOX,
        organization_id=org.id,
    )
    db_session.add(env)
    db_session.commit()

    return user, org, env


def test_create_integration_config(db_session, user_org_env):
    _, _, env = user_org_env

    credentials_data = {"slack_token": "123456"}
    integration_config = IntegrationConfig(
        environment_id=env.id,
        source=IntegrationSource.SLACK,
        integration_type=IntegrationType.MESSAGING,
        credentials=credentials_data,
        is_active=True,
    )
    db_session.add(integration_config)
    db_session.commit()

    result = (
        db_session.query(IntegrationConfig).filter_by(environment_id=env.id).first()
    )
    assert result is not None
    assert result.source == IntegrationSource.SLACK
    assert result.credentials == credentials_data


def test_unique_source_constraint(db_session, user_org_env):
    _, _, env = user_org_env

    integration_config1 = IntegrationConfig(
        environment_id=env.id,
        source=IntegrationSource.SLACK,
        integration_type=IntegrationType.MESSAGING,
        credentials={"slack_token": "value"},
        is_active=True,
    )
    db_session.add(integration_config1)
    db_session.commit()

    integration_config2 = IntegrationConfig(
        environment_id=env.id,
        source=IntegrationSource.SLACK,
        credentials={"slack_token": "another_value"},
        is_active=True,
    )
    db_session.add(integration_config2)

    with pytest.raises(IntegrityError):
        db_session.commit()


def test_validate_credentials_returns_none_when_none():
    config = IntegrationConfig()
    config.source = IntegrationSource.SALESFORCE

    result = config.validate_credentials("credentials", None)
    assert result is None


def test_validate_credentials_returns_empty_dict_when_empty_dict():
    config = IntegrationConfig()
    config.source = IntegrationSource.SALESFORCE

    result = config.validate_credentials("credentials", {})
    assert result == {}


def test_validate_credentials_returns_value_when_source_is_none():
    config = IntegrationConfig()
    config.source = None

    credentials = {"key": "value"}
    result = config.validate_credentials("credentials", credentials)
    assert result == credentials


def test_validate_credentials_returns_value_when_source_not_set():
    config = IntegrationConfig()

    credentials = {"key": "value"}
    result = config.validate_credentials("credentials", credentials)
    assert result == credentials


def test_validate_settings_returns_none_when_none():
    config = IntegrationConfig()
    config.source = IntegrationSource.SALESFORCE

    result = config.validate_settings("settings", None)
    assert result is None


def test_validate_settings_returns_empty_dict_when_empty_dict():
    config = IntegrationConfig()
    config.source = IntegrationSource.SALESFORCE

    result = config.validate_settings("settings", {})
    assert result == {}


def test_validate_settings_returns_value_when_source_is_none():
    config = IntegrationConfig()
    config.source = None

    settings = {"option": "value"}
    result = config.validate_settings("settings", settings)
    assert result == settings


def test_validate_settings_returns_value_when_source_not_set():
    config = IntegrationConfig()

    settings = {"option": "value"}
    result = config.validate_settings("settings", settings)
    assert result == settings
