import uuid

import pytest

from app.workspace.schemas import Account<PERSON><PERSON>, UserCRMInfo
from app.workspace.services.crm import CRMService


@pytest.mark.anyio
async def test_get_crm_with_valid_integration(mocker):
    mock_crm_provider = mocker.Mock()
    mock_crm_provider.source = "salesforce"

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = mock_crm_provider
    mock_user_integrations.crm_user_id.return_value = "crm_user_id1"

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_crm()

    assert result is not None
    assert isinstance(result, UserCRMInfo)
    assert result.crm_name == "salesforce"
    assert result.crm_user_id == "crm_user_id1"
    mock_user_integrations.crm.assert_called_once()


@pytest.mark.anyio
async def test_get_crm_without_crm_provider(mocker):
    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = None
    mock_user_integrations.crm_user_id.return_value = "crm_user_id1"

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_crm()

    assert result is None
    mock_user_integrations.crm.assert_called_once()


@pytest.mark.anyio
async def test_get_crm_without_crm_user_id(mocker):
    mock_crm_provider = mocker.Mock()
    mock_crm_provider.source = "salesforce"

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = mock_crm_provider
    mock_user_integrations.crm_user_id.return_value = None

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_crm()

    assert result is None
    mock_user_integrations.crm.assert_called_once()


@pytest.mark.anyio
async def test_get_crm_without_both_provider_and_user_id(mocker):
    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = None
    mock_user_integrations.crm_user_id.return_value = None

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_crm()

    assert result is None
    mock_user_integrations.crm.assert_called_once()


@pytest.mark.anyio
async def test_get_accounts(mocker):
    mock_crm_provider = mocker.Mock()
    mock_crm_provider.list_account_access = mocker.AsyncMock(
        return_value=[
            {
                "Id": "1",
                "Name": "Account1",
            },
            {
                "Id": "2",
                "Name": "Account2",
            },
        ]
    )

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = mock_crm_provider
    mock_user_integrations.crm_user_id.return_value = "crm_user_id1"

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_accounts()

    assert len(result) == 2
    assert all(isinstance(account, AccountRead) for account in result)

    ids = [account.crm_id for account in result]
    assert "1" in ids
    assert "2" in ids

    names = [account.crm_name for account in result]
    assert "Account1" in names
    assert "Account2" in names

    mock_crm_provider.list_account_access.assert_called_once_with(
        crm_user_id="crm_user_id1"
    )


@pytest.mark.anyio
async def test_get_accounts_no_crm_provider(mocker):
    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = None
    mock_user_integrations.crm_user_id.return_value = "crm_user_id1"

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_accounts()

    assert result == []


@pytest.mark.anyio
async def test_get_accounts_no_crm_user_id(mocker):
    mock_crm_provider = mocker.Mock()
    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = mock_crm_provider
    mock_user_integrations.crm_user_id.return_value = None

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_accounts()

    assert result == []


@pytest.mark.anyio
async def test_sync_accounts(mocker):
    mock_user_integrations = mocker.AsyncMock()
    mock_field_mapping_repo = mocker.Mock()
    user_id = uuid.uuid4()

    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    await service.sync_accounts()

    mock_user_integrations.sync_crm_accounts.assert_called_once()
