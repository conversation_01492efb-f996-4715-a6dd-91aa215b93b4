import uuid
from datetime import datetime

import pytest

from app.integrations.types import IntegrationSource
from app.workspace.models import IntegrationConfig, IntegrationUser
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.types import EnvironmentType


@pytest.fixture
def mock_integration_cfg_repo(mocker):
    return mocker.Mock(spec=IntegrationConfigRepository)


@pytest.fixture
def mock_integration_user_repo(mocker):
    return mocker.Mock(spec=IntegrationUserRepository)


@pytest.fixture
def config_service(mock_integration_cfg_repo, mock_integration_user_repo):
    return IntegrationConfigService(
        integration_cfg_repo=mock_integration_cfg_repo,
        integration_user_repo=mock_integration_user_repo,
    )


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.mark.anyio
async def test_get_integration_configs(
    config_service, mock_integration_cfg_repo, mock_environment, mocker
):
    expected_configs = [
        mocker.Mock(spec=IntegrationConfig),
        mocker.Mock(spec=IntegrationConfig),
    ]
    mock_integration_cfg_repo.get_all_by_org_id = mocker.AsyncMock(
        return_value=expected_configs
    )

    result = await config_service.get_integration_configs(mock_environment)

    assert result == expected_configs
    mock_integration_cfg_repo.get_all_by_org_id.assert_called_once_with(
        org_id=mock_environment.organization_id, env_type=mock_environment.type
    )


@pytest.mark.anyio
async def test_get_integration_config_found(
    config_service, mock_integration_cfg_repo, mock_environment, mocker
):
    source = IntegrationSource.SALESFORCE

    config1 = mocker.Mock(spec=IntegrationConfig)
    config1.source = IntegrationSource.SLACK

    config2 = mocker.Mock(spec=IntegrationConfig)
    config2.source = IntegrationSource.SALESFORCE

    mock_integration_cfg_repo.get_all_by_org_id = mocker.AsyncMock(
        return_value=[config1, config2]
    )

    result = await config_service.get_integration_config(mock_environment, source)

    assert result == config2
    mock_integration_cfg_repo.get_all_by_org_id.assert_called_once_with(
        org_id=mock_environment.organization_id, env_type=mock_environment.type
    )


@pytest.mark.anyio
async def test_get_integration_config_not_found(
    config_service, mock_integration_cfg_repo, mock_environment, mocker
):
    source = IntegrationSource.SALESFORCE

    config1 = mocker.Mock(spec=IntegrationConfig)
    config1.source = IntegrationSource.SLACK

    mock_integration_cfg_repo.get_all_by_org_id = mocker.AsyncMock(
        return_value=[config1]
    )

    result = await config_service.get_integration_config(mock_environment, source)

    assert result is None
    mock_integration_cfg_repo.get_all_by_org_id.assert_called_once_with(
        org_id=mock_environment.organization_id, env_type=mock_environment.type
    )


@pytest.mark.anyio
async def test_get_crm_config(
    config_service, mock_integration_cfg_repo, mock_environment, mocker
):
    config1 = mocker.Mock(spec=IntegrationConfig)
    config1.source = IntegrationSource.SLACK

    config2 = mocker.Mock(spec=IntegrationConfig)
    config2.source = IntegrationSource.SALESFORCE

    mock_integration_cfg_repo.get_all_by_org_id = mocker.AsyncMock(
        return_value=[config1, config2]
    )

    result = await config_service.get_crm_config(mock_environment)

    assert result == config2
    mock_integration_cfg_repo.get_all_by_org_id.assert_called_once_with(
        org_id=mock_environment.organization_id, env_type=mock_environment.type
    )


@pytest.mark.anyio
async def test_get_crm_config_not_found(
    config_service, mock_integration_cfg_repo, mock_environment, mocker
):
    config1 = mocker.Mock(spec=IntegrationConfig)
    config1.source = IntegrationSource.SLACK

    mock_integration_cfg_repo.get_all_by_org_id = mocker.AsyncMock(
        return_value=[config1]
    )

    result = await config_service.get_crm_config(mock_environment)

    assert result is None
    mock_integration_cfg_repo.get_all_by_org_id.assert_called_once_with(
        org_id=mock_environment.organization_id, env_type=mock_environment.type
    )


@pytest.mark.anyio
async def test_get_integration_user(config_service, mock_integration_user_repo, mocker):
    integration_config_id = uuid.uuid4()
    user_id = uuid.uuid4()
    expected_integration_user = mocker.Mock(spec=IntegrationUser)
    mock_integration_user_repo.get_by_user_and_integration.return_value = (
        expected_integration_user
    )

    result = await config_service.get_integration_user(integration_config_id, user_id)

    assert result == expected_integration_user
    mock_integration_user_repo.get_by_user_and_integration.assert_called_once_with(
        integration_config_id=integration_config_id, user_id=user_id
    )
