import uuid
from datetime import UTC, datetime, timedelta

import pytest
from pydantic import BaseModel

from app.common.oauth.flow_manager import OAuthFlowType
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationCredentialsError,
    IntegrationTokenNotFoundError,
)
from app.workspace.models import IntegrationConfig, IntegrationUser
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.base.oauth_connection import (
    BaseOAuthConnection,
    BaseOAuthCredentials,
)
from app.workspace.types import EnvironmentType


class MockTokenResponse(BaseModel):
    external_user_id: str
    access_token: str
    expires_at: datetime


class MockOAuthConnectionService(
    BaseOAuthConnection[BaseOAuthCredentials, MockTokenResponse]
):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._test_integration_source = IntegrationSource.SALESFORCE
        self._test_default_scope = "test_scope"
        self._test_default_token_expiry = 3600

    @property
    def integration_source(self) -> IntegrationSource:
        return self._test_integration_source

    @property
    def default_scope(self) -> str:
        return self._test_default_scope

    @property
    def default_token_expiry_seconds(self) -> int:
        return self._test_default_token_expiry

    async def _get_config_and_credentials(
        self, _: OrgEnvironment
    ) -> tuple[IntegrationConfig, BaseOAuthCredentials]:
        return IntegrationConfig(), BaseOAuthCredentials(client_id="", client_secret="")

    def _validate_credentials(self, credentials: BaseOAuthCredentials) -> None:
        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError("Missing client_id and client_secret")

    async def _extract_user_info_from_token(
        self, token_data: dict, _: BaseOAuthCredentials
    ) -> dict[str, str]:
        return {"external_user_id": token_data.get("user_id", "test_user")}

    def _get_redirect_uri_for_token_exchange(self) -> str:
        return self.redirect_uri

    def _create_token_response(
        self, integration_user, expires_at: datetime
    ) -> MockTokenResponse:
        return MockTokenResponse(
            external_user_id=integration_user.external_user_id,
            access_token=integration_user.access_token,
            expires_at=expires_at,
        )


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )


@pytest.fixture
def service_mocks(mocker):
    db_session_mock = mocker.Mock()
    db_session_mock.commit = mocker.AsyncMock()
    db_session_mock.refresh = mocker.AsyncMock()

    return {
        "db_session": db_session_mock,
        "integration_user_repo": mocker.Mock(),
        "integration_cfg_repo": mocker.Mock(),
        "oauth_flow_manager": mocker.Mock(),
    }


@pytest.fixture
def oauth_connection_service(service_mocks):
    service = MockOAuthConnectionService(
        db_session=service_mocks["db_session"],
        integration_user_repo=service_mocks["integration_user_repo"],
        integration_cfg_repo=service_mocks["integration_cfg_repo"],
        auth_url="https://example.com/oauth/authorize",
        token_url="https://example.com/oauth/token",  # noqa: S106
        redirect_uri="https://app.example.com/oauth/callback",
        flow_type=OAuthFlowType.STANDARD,
    )
    service.oauth_flow_manager = service_mocks["oauth_flow_manager"]
    return service


@pytest.fixture
def test_data(mock_environment):
    user_id = uuid.uuid4()
    integration_config_id = uuid.uuid4()
    integration_user_id = uuid.uuid4()

    integration_config = IntegrationConfig()
    integration_config.id = integration_config_id
    integration_config.organization_id = mock_environment.organization_id
    integration_config.source = IntegrationSource.SALESFORCE
    integration_config.credentials = {
        "client_id": "test_client_id",
        "client_secret": "test_client_secret",
    }

    integration_user = IntegrationUser()
    integration_user.id = integration_user_id
    integration_user.user_id = user_id
    integration_user.integration_config_id = integration_config_id
    integration_user.external_user_id = "test_user_123"
    integration_user.access_token = "test_access_token"
    integration_user.refresh_token = "test_refresh_token"
    integration_user.scope = "test_scope"
    integration_user.token_type = "Bearer"
    integration_user.expires_at = datetime.now(UTC) + timedelta(hours=1)
    integration_user.last_refreshed_at = datetime.now(UTC)

    return {
        "user_id": user_id,
        "environment": mock_environment,
        "integration_config_id": integration_config_id,
        "integration_user_id": integration_user_id,
        "integration_config": integration_config,
        "integration_user": integration_user,
    }


@pytest.mark.anyio
async def test_generate_oauth_authorization_uri_success(
    mocker, oauth_connection_service, service_mocks, test_data
):
    oauth_connection_service._get_config_and_credentials = mocker.AsyncMock(
        return_value=(
            test_data["integration_config"],
            BaseOAuthCredentials(
                client_id="test_client_id",
                client_secret="test_client_secret",  # noqa: S106
            ),
        )
    )

    service_mocks[
        "oauth_flow_manager"
    ].generate_authorization_uri.return_value = "https://example.com/auth"

    result = await oauth_connection_service.generate_oauth_authorization_uri(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
    )

    assert result == "https://example.com/auth"
    oauth_connection_service._get_config_and_credentials.assert_called_once_with(
        test_data["environment"]
    )
    service_mocks["oauth_flow_manager"].generate_authorization_uri.assert_called_once()


@pytest.mark.anyio
async def test_generate_oauth_authorization_uri_with_custom_scope(
    mocker, oauth_connection_service, service_mocks, test_data
):
    oauth_connection_service._get_config_and_credentials = mocker.AsyncMock(
        return_value=(
            test_data["integration_config"],
            BaseOAuthCredentials(
                client_id="test_client_id",
                client_secret="test_client_secret",  # noqa: S106
            ),
        )
    )

    service_mocks[
        "oauth_flow_manager"
    ].generate_authorization_uri.return_value = "https://example.com/auth"

    custom_scope = "custom_scope read write"
    await oauth_connection_service.generate_oauth_authorization_uri(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
        scope=custom_scope,
    )

    call_args = service_mocks["oauth_flow_manager"].generate_authorization_uri.call_args
    assert call_args.kwargs["scope"] == custom_scope


@pytest.mark.anyio
async def test_generate_oauth_authorization_uri_missing_credentials(
    mocker, oauth_connection_service, test_data
):
    oauth_connection_service._get_config_and_credentials = mocker.AsyncMock(
        return_value=(
            test_data["integration_config"],
            BaseOAuthCredentials(client_id="", client_secret=""),
        )
    )

    with pytest.raises(IntegrationCredentialsError):
        await oauth_connection_service.generate_oauth_authorization_uri(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_process_oauth_callback_new_user(
    mocker, oauth_connection_service, service_mocks, test_data
):
    oauth_connection_service._get_config_and_credentials = mocker.AsyncMock(
        return_value=(
            test_data["integration_config"],
            BaseOAuthCredentials(
                client_id="test_client_id",
                client_secret="test_client_secret",  # noqa: S106
            ),
        )
    )

    token_data = {
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token",
        "expires_in": 3600,
        "scope": "test_scope",
        "token_type": "Bearer",
        "user_id": "test_user_123",
    }
    service_mocks["oauth_flow_manager"].exchange_code_for_token = mocker.AsyncMock(
        return_value=token_data
    )

    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration = mocker.AsyncMock(return_value=None)

    created_integration_user = IntegrationUser()
    created_integration_user.external_user_id = "test_user_123"
    created_integration_user.access_token = "new_access_token"
    created_integration_user.refresh_token = "new_refresh_token"
    created_integration_user.scope = "test_scope"
    created_integration_user.token_type = "Bearer"
    created_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=1)

    service_mocks["integration_user_repo"].create = mocker.AsyncMock(
        return_value=created_integration_user
    )

    result = await oauth_connection_service.process_oauth_callback(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
        code="auth_code",
        state="state_value",
    )

    assert isinstance(result, MockTokenResponse)
    assert result.external_user_id == "test_user_123"
    assert result.access_token == "new_access_token"

    service_mocks["integration_user_repo"].create.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()
    service_mocks["db_session"].refresh.assert_called_once_with(
        created_integration_user
    )


@pytest.mark.anyio
async def test_process_oauth_callback_existing_user(
    mocker, oauth_connection_service, service_mocks, test_data
):
    oauth_connection_service._get_config_and_credentials = mocker.AsyncMock(
        return_value=(
            test_data["integration_config"],
            BaseOAuthCredentials(
                client_id="test_client_id",
                client_secret="test_client_secret",  # noqa: S106
            ),
        )
    )

    token_data = {
        "access_token": "updated_access_token",
        "refresh_token": "updated_refresh_token",
        "expires_in": 3600,
        "user_id": "test_user_123",
    }
    service_mocks["oauth_flow_manager"].exchange_code_for_token = mocker.AsyncMock(
        return_value=token_data
    )

    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )

    updated_integration_user = IntegrationUser()
    updated_integration_user.external_user_id = "test_user_123"
    updated_integration_user.access_token = "updated_access_token"
    updated_integration_user.refresh_token = "updated_refresh_token"
    updated_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=1)

    service_mocks["integration_user_repo"].update = mocker.AsyncMock(
        return_value=updated_integration_user
    )

    result = await oauth_connection_service.process_oauth_callback(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
        code="auth_code",
        state="state_value",
    )

    assert isinstance(result, MockTokenResponse)
    assert result.external_user_id == "test_user_123"
    assert result.access_token == "updated_access_token"

    service_mocks["integration_user_repo"].update.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()
    service_mocks["db_session"].refresh.assert_called_once_with(
        updated_integration_user
    )


@pytest.mark.anyio
async def test_refresh_access_token_success(
    mocker, oauth_connection_service, service_mocks, test_data
):
    oauth_connection_service._get_config_and_credentials = mocker.AsyncMock(
        return_value=(
            test_data["integration_config"],
            BaseOAuthCredentials(
                client_id="test_client_id",
                client_secret="test_client_secret",  # noqa: S106
            ),
        )
    )

    service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )

    token_data = {
        "access_token": "refreshed_access_token",
        "expires_in": 3600,
        "scope": "test_scope",
        "token_type": "Bearer",
    }
    service_mocks["oauth_flow_manager"].refresh_access_token = mocker.AsyncMock(
        return_value=token_data
    )

    refreshed_integration_user = IntegrationUser()
    refreshed_integration_user.external_user_id = "test_user_123"
    refreshed_integration_user.access_token = "refreshed_access_token"
    refreshed_integration_user.refresh_token = "test_refresh_token"
    refreshed_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=1)

    service_mocks["integration_user_repo"].update = mocker.AsyncMock(
        return_value=refreshed_integration_user
    )

    result = await oauth_connection_service.refresh_access_token(
        integration_user_id=test_data["integration_user_id"],
        environment=test_data["environment"],
    )

    assert isinstance(result, MockTokenResponse)
    assert result.external_user_id == "test_user_123"
    assert result.access_token == "refreshed_access_token"

    service_mocks["integration_user_repo"].get_by_id.assert_called_once_with(
        test_data["integration_user_id"]
    )
    service_mocks["oauth_flow_manager"].refresh_access_token.assert_called_once()
    service_mocks["integration_user_repo"].update.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()
    service_mocks["db_session"].refresh.assert_called_once_with(
        refreshed_integration_user
    )


@pytest.mark.anyio
async def test_refresh_access_token_user_not_found(
    mocker, oauth_connection_service, service_mocks, test_data
):
    service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
        return_value=None
    )

    with pytest.raises(IntegrationTokenNotFoundError):
        await oauth_connection_service.refresh_access_token(
            integration_user_id=test_data["integration_user_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_refresh_access_token_missing_credentials(
    mocker, oauth_connection_service, service_mocks, test_data
):
    service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )

    oauth_connection_service._get_config_and_credentials = mocker.AsyncMock(
        return_value=(
            test_data["integration_config"],
            BaseOAuthCredentials(client_id="", client_secret=""),
        )
    )

    with pytest.raises(IntegrationCredentialsError):
        await oauth_connection_service.refresh_access_token(
            integration_user_id=test_data["integration_user_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_remove_connection_success(
    mocker, oauth_connection_service, service_mocks, test_data
):
    oauth_connection_service._get_config_and_credentials = mocker.AsyncMock(
        return_value=(
            test_data["integration_config"],
            BaseOAuthCredentials(
                client_id="test_client_id",
                client_secret="test_client_secret",  # noqa: S106
            ),
        )
    )

    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )
    service_mocks["integration_user_repo"].delete = mocker.AsyncMock()

    await oauth_connection_service.remove_connection(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
    )

    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration.assert_called_once_with(
        user_id=test_data["user_id"],
        integration_config_id=test_data["integration_config"].id,
    )
    service_mocks["integration_user_repo"].delete.assert_called_once_with(
        test_data["integration_user"].id
    )
    service_mocks["db_session"].commit.assert_called_once()


@pytest.mark.anyio
async def test_remove_connection_not_found(
    mocker, oauth_connection_service, service_mocks, test_data
):
    oauth_connection_service._get_config_and_credentials = mocker.AsyncMock(
        return_value=(
            test_data["integration_config"],
            BaseOAuthCredentials(
                client_id="test_client_id",
                client_secret="test_client_secret",  # noqa: S106
            ),
        )
    )

    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration = mocker.AsyncMock(return_value=None)

    with pytest.raises(IntegrationTokenNotFoundError):
        await oauth_connection_service.remove_connection(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_create_or_update_integration_user_create_new(
    mocker, oauth_connection_service, service_mocks, test_data
):
    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration = mocker.AsyncMock(return_value=None)

    created_user = IntegrationUser()
    service_mocks["integration_user_repo"].create = mocker.AsyncMock(
        return_value=created_user
    )

    token_data = {
        "access_token": "test_token",
        "refresh_token": "test_refresh",
        "scope": "test_scope",
        "token_type": "Bearer",
    }
    user_info = {"external_user_id": "test_user_123"}
    expires_at = datetime.now(UTC) + timedelta(hours=1)
    now = datetime.now(UTC)

    result = await oauth_connection_service._create_or_update_integration_user(
        user_id=test_data["user_id"],
        integration_config=test_data["integration_config"],
        token_data=token_data,
        user_info=user_info,
        expires_at=expires_at,
        now=now,
    )

    assert result == created_user
    service_mocks["integration_user_repo"].create.assert_called_once()
    create_call_args = service_mocks["integration_user_repo"].create.call_args
    assert create_call_args.kwargs["user_id"] == test_data["user_id"]
    assert (
        create_call_args.kwargs["integration_config_id"]
        == test_data["integration_config"].id
    )


@pytest.mark.anyio
async def test_create_or_update_integration_user_update_existing(
    mocker, oauth_connection_service, service_mocks, test_data
):
    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )

    updated_user = IntegrationUser()
    service_mocks["integration_user_repo"].update = mocker.AsyncMock(
        return_value=updated_user
    )

    token_data = {
        "access_token": "updated_token",
        "refresh_token": "updated_refresh",
        "scope": "updated_scope",
        "token_type": "Bearer",
    }
    user_info = {"external_user_id": "test_user_123", "external_org_id": "test_org_456"}
    expires_at = datetime.now(UTC) + timedelta(hours=1)
    now = datetime.now(UTC)

    result = await oauth_connection_service._create_or_update_integration_user(
        user_id=test_data["user_id"],
        integration_config=test_data["integration_config"],
        token_data=token_data,
        user_info=user_info,
        expires_at=expires_at,
        now=now,
    )

    assert result == updated_user
    service_mocks["integration_user_repo"].update.assert_called_once()
    update_call_args = service_mocks["integration_user_repo"].update.call_args
    assert update_call_args.args[0] == test_data["integration_user"].id
    assert update_call_args.kwargs["external_org_id"] == "test_org_456"
