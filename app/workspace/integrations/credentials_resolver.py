import uuid
from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from app.workspace.services.hubspot_connection import HubSpotConnectionService

from app.common.helpers.logger import get_logger
from app.integrations.base.credentials_resolver import (
    ICredentials,
    ICredentialsResolver,
)
from app.integrations.types import IntegrationSource
from app.workspace.models import IntegrationConfig, IntegrationUser
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.salesforce_connection import SalesforceConnectionService

logger = get_logger()


class WorkspaceCredentials(ICredentials):
    def __init__(self, secrets: dict[str, Any]):
        self._secrets = secrets

    @property
    def secrets(self) -> dict[str, Any]:
        return self._secrets

    async def refresh_token(self) -> "ICredentials":
        return self


class WorkspaceSalesforceCredentials(ICredentials):
    def __init__(
        self,
        integration_user: IntegrationU<PERSON>,
        connection_service: SalesforceConnectionService,
        environment: OrgEnvironment,
    ):
        self._integration_user = integration_user
        self._connection_service = connection_service
        self._environment = environment
        self._integration_user_id = integration_user.id
        self._user_id = integration_user.user_id

    @property
    def secrets(self) -> dict[str, Any]:
        return {
            "access_token": self._integration_user.access_token,
            "instance_url": self._integration_user.instance_url,
        }

    async def refresh_token(self) -> "WorkspaceSalesforceCredentials":
        try:
            token_response = await self._connection_service.refresh_access_token(
                integration_user_id=self._integration_user_id,
                environment=self._environment,
            )
            self._integration_user.access_token = token_response.access_token
            if token_response.instance_url:
                self._integration_user.instance_url = token_response.instance_url
            logger.info(f"Successfully refreshed token for user {self._user_id}")
        except Exception as e:
            logger.error(f"Failed to refresh token for user {self._user_id}: {e}")

        return self


class WorkspaceHubSpotCredentials(ICredentials):
    def __init__(
        self,
        integration_user: IntegrationUser,
        connection_service: "HubSpotConnectionService",
        environment: OrgEnvironment,
    ):
        self._integration_user = integration_user
        self._connection_service = connection_service
        self._environment = environment
        self._integration_user_id = integration_user.id
        self._user_id = integration_user.user_id

    @property
    def secrets(self) -> dict[str, Any]:
        return {
            "access_token": self._integration_user.access_token,
        }

    async def refresh_token(self) -> "WorkspaceHubSpotCredentials":
        try:
            token_response = await self._connection_service.refresh_access_token(
                integration_user_id=self._integration_user_id,
                environment=self._environment,
            )
            self._integration_user.access_token = token_response.access_token
            logger.info(
                f"Successfully refreshed HubSpot token for user {self._user_id}"
            )
        except Exception as e:
            logger.error(
                f"Failed to refresh HubSpot token for user {self._user_id}: {e}"
            )

        return self


class WorkspaceGoogleCalendarCredentials(ICredentials):
    def __init__(
        self, integration_user: IntegrationUser, integration_config: IntegrationConfig
    ):
        self._integration_user = integration_user
        self._integration_config = integration_config

    @property
    def secrets(self) -> dict[str, Any]:
        return {
            "access_token": self._integration_user.access_token,
            "refresh_token": self._integration_user.refresh_token,
            "scope": self._integration_user.scope,
            "client_id": self._integration_config.credentials.get("client_id"),
            "client_secret": self._integration_config.credentials.get("client_secret"),
        }

    async def refresh_token(self) -> "WorkspaceGoogleCalendarCredentials":
        # TODO: implement token refresh for Google Calendar
        logger.info("Refresh token not implemented for Google Calendar")
        return self


class SimpleCredentialsResolver(ICredentialsResolver):
    def __init__(self, credentials: ICredentials):
        self.credentials = credentials

    async def get_credentials(self, *_args, **_kwargs) -> ICredentials:
        return self.credentials


class OrganizationCredentialsResolver(ICredentialsResolver):
    """
    Resolver that uses organization-level credentials.
    """

    def __init__(
        self,
        environment: OrgEnvironment,
        integration_config_service: IntegrationConfigService,
    ):
        self.environment = environment
        self.integration_config_service = integration_config_service

    async def get_credentials(self, source: IntegrationSource) -> WorkspaceCredentials:
        integration_config = (
            await self.integration_config_service.get_integration_config(
                environment=self.environment, source=source
            )
        )

        if not integration_config:
            logger.warning(
                f"No credentials found for org {self.environment.organization_id}, source {source}"
            )
            return WorkspaceCredentials({})

        return WorkspaceCredentials(integration_config.credentials)


class UserCredentialsResolver(ICredentialsResolver):
    """
    Resolver that uses user-specific credentials when available.
    For OAuth-supporting sources: returns empty credentials if no user token exists.
    For other sources: falls back to organization credentials automatically.
    """

    def __init__(
        self,
        environment: OrgEnvironment,
        user_id: uuid.UUID,
        integration_config_service: IntegrationConfigService,
        salesforce_connection_service: SalesforceConnectionService,
        hubspot_connection_service: "HubSpotConnectionService",
    ):
        self.environment = environment
        self.user_id = user_id
        self.integration_config_service = integration_config_service
        self.salesforce_connection_service = salesforce_connection_service
        self.hubspot_connection_service = hubspot_connection_service

    async def get_credentials(
        self, source: IntegrationSource
    ) -> (
        WorkspaceCredentials
        | WorkspaceSalesforceCredentials
        | WorkspaceHubSpotCredentials
        | WorkspaceGoogleCalendarCredentials
    ):
        integration_config = (
            await self.integration_config_service.get_integration_config(
                environment=self.environment, source=source
            )
        )

        if not integration_config:
            logger.warning(
                f"No integration config found for org {self.environment.organization_id}, source {source}"
            )
            return WorkspaceCredentials({})

        if source is IntegrationSource.SALESFORCE:
            integration_user = (
                await self.integration_config_service.get_integration_user(
                    integration_config_id=integration_config.id,
                    user_id=self.user_id,
                )
            )
            if integration_user:
                return WorkspaceSalesforceCredentials(
                    integration_user=integration_user,
                    environment=self.environment,
                    connection_service=self.salesforce_connection_service,
                )

            logger.warning(
                f"No integration user found for user {self.user_id}, source {source}"
            )
            return WorkspaceCredentials({})

        if source is IntegrationSource.HUBSPOT:
            integration_user = (
                await self.integration_config_service.get_integration_user(
                    integration_config_id=integration_config.id,
                    user_id=self.user_id,
                )
            )
            if integration_user:
                return WorkspaceHubSpotCredentials(
                    integration_user=integration_user,
                    environment=self.environment,
                    connection_service=self.hubspot_connection_service,
                )

            logger.warning(
                f"No integration user found for user {self.user_id}, source {source}"
            )
            return WorkspaceCredentials({})

        if source is IntegrationSource.GOOGLE_CALENDAR:
            integration_user = (
                await self.integration_config_service.get_integration_user(
                    integration_config_id=integration_config.id,
                    user_id=self.user_id,
                )
            )
            if integration_user:
                return WorkspaceGoogleCalendarCredentials(
                    integration_user, integration_config
                )

        return WorkspaceCredentials(integration_config.credentials)
