from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.types import IntegrationSource
from app.workspace.schemas.integration import (
    BaseCredentials,
    BaseSettings,
    SalesforceCredentials,
    SalesforceSettings,
    SlackCredentials,
    SlackSettings,
)

logger = get_logger()


def validate_credentials(
    source: IntegrationSource, credentials: dict[str, Any]
) -> dict[str, Any]:
    if not credentials:
        return {}
    credentials_classes: dict[IntegrationSource, type[BaseCredentials]] = {
        IntegrationSource.SALESFORCE: SalesforceCredentials,
        IntegrationSource.SLACK: SlackCredentials,
    }
    try:
        cls = credentials_classes.get(source)
        if cls:
            return cls.model_validate(credentials).model_dump(exclude_none=True)
        else:
            return credentials
    except Exception as e:
        logger.exception(f"Validation error for {source} credentials")
        raise ValueError(f"Invalid credentials for {source}: {str(e)}")


def validate_settings(
    source: IntegrationSource, settings: dict[str, Any]
) -> dict[str, Any]:
    if not settings:
        return {}
    settings_classes: dict[IntegrationSource, type[BaseSettings]] = {
        IntegrationSource.SALESFORCE: SalesforceSettings,
        IntegrationSource.SLACK: SlackSettings,
    }
    try:
        cls = settings_classes.get(source)
        if cls:
            return cls.model_validate(settings).model_dump(exclude_none=True)
        else:
            return settings
    except Exception as e:
        logger.exception(f"Validation error for {source} settings")
        raise ValueError(f"Invalid settings for {source}: {str(e)}")
