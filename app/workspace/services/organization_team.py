from collections.abc import Sequence
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.repository import UserRepository
from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.repositories.organization_member import OrganizationMemberRepository
from app.workspace.schemas import OrganizationMemberProfile, OrganizationMemberRead


class OrganizationTeamService:
    def __init__(
        self,
        db_session: AsyncSession,
        org_member_repo: OrganizationMemberRepository,
        org_repo: OrganizationRepository,
        user_repo: UserRepository,
    ):
        self.db_session = db_session
        self.org_member_repo = org_member_repo
        self.org_repo = org_repo
        self.user_repo = user_repo

    async def get_team_user_ids(self, org_id: UUID) -> Sequence[UUID]:
        members = await self.org_member_repo.get_by_organization_id(org_id)
        member_user_ids = [member.user_id for member in members]

        org = await self.org_repo.get_by_id(org_id)
        if org and org.owner_id and org.owner_id not in member_user_ids:
            member_user_ids.append(org.owner_id)

        return member_user_ids

    async def get_team_member(self, user_id: UUID) -> OrganizationMemberRead | None:
        member = await self.org_member_repo.get_by_user_id(user_id)

        if not member:
            return None

        return OrganizationMemberRead.model_validate(member)

    async def get_team_member_profile(self, user_id: UUID) -> OrganizationMemberProfile:
        member = await self.org_member_repo.get_by_user_id(user_id)
        user = await self.user_repo.get_by_id(user_id)
        if not member or not user:
            raise ValueError(f"Member or user not found with user_id: {user_id}")
        return OrganizationMemberProfile(
            id=member.id,
            user_id=user.id,
            organization_id=member.organization_id,
            first_name=user.first_name,
            last_name=user.last_name,
            email=user.email,
            is_admin=member.is_admin,
        )

    async def get_team_member_profiles(
        self, org_id: UUID
    ) -> list[OrganizationMemberProfile]:
        members = await self.org_member_repo.get_by_organization_id(org_id)
        users = await self.user_repo.get_by_ids([member.user_id for member in members])
        user_map = {user.id: user for user in users}

        profiles = []
        for member in members:
            user = user_map.get(member.user_id)
            if user:
                profiles.append(
                    OrganizationMemberProfile(
                        id=member.id,
                        user_id=user.id,
                        organization_id=member.organization_id,
                        first_name=user.first_name,
                        last_name=user.last_name,
                        email=user.email,
                        is_admin=member.is_admin,
                    )
                )

        return profiles
