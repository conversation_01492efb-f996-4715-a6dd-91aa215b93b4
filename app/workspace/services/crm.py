from uuid import UUID

from app.workspace.integrations.user_integrations import (
    UserIntegrations,
)
from app.workspace.repositories.salesforce_field_mapping import (
    SalesforceFieldMappingRepository,
)
from app.workspace.schemas import AccountRead, Metrics, UserCRMInfo


class CRMService:
    def __init__(
        self,
        user_id: UUID,
        user_integrations: UserIntegrations,
        field_mapping_repo: SalesforceFieldMappingRepository,
    ):
        self.user_id = user_id
        self.user_integrations = user_integrations
        self.field_mapping_repo = field_mapping_repo

    async def get_crm(self) -> UserCRMInfo | None:
        crm_provider = await self.user_integrations.crm()
        crm_user_id = await self.user_integrations.crm_user_id()
        if not crm_provider or not crm_user_id:
            return None
        return UserCRMInfo(crm_name=crm_provider.source, crm_user_id=crm_user_id)

    async def get_accounts(self) -> list[AccountRead]:
        provider = await self.user_integrations.crm()
        crm_user_id = await self.user_integrations.crm_user_id()
        if not provider or not crm_user_id:
            return []

        accounts = await provider.list_account_access(crm_user_id=crm_user_id)
        return [
            AccountRead(crm_id=account["Id"], crm_name=account["Name"])
            for account in accounts
        ]

    async def sync_accounts(self) -> None:
        await self.user_integrations.sync_crm_accounts()

    async def get_metrics(self) -> Metrics:
        provider = await self.user_integrations.crm()
        crm_user_id = await self.user_integrations.crm_user_id()
        if not provider or not crm_user_id:
            return Metrics(quota=0, closed_won=0, pipeline=0, forecast=0)

        organization_id = self.user_integrations.org_id
        field_mapping = await self.field_mapping_repo.get_or_create_default(
            organization_id
        )

        mapping_dict = {
            "opportunity_amount_field": field_mapping.opportunity_amount_field,
            "opportunity_stage_field": field_mapping.opportunity_stage_field,
            "opportunity_owner_field": field_mapping.opportunity_owner_field,
            "opportunity_probability_field": field_mapping.opportunity_probability_field,
            "closed_won_stage_pattern": field_mapping.closed_won_stage_pattern,
            "closed_lost_stage_pattern": field_mapping.closed_lost_stage_pattern,
            "forecast_probability_multiplier": field_mapping.forecast_probability_multiplier,
            "use_probability_field": field_mapping.use_probability_field,
            "quota_table": field_mapping.quota_object,
            "quota_amount_field": field_mapping.quota_amount_field,
            "quota_owner_field": field_mapping.quota_user_field,
            "metrics_months_limit": field_mapping.metrics_months_limit,
        }

        metrics = await provider.get_metrics(
            crm_user_id=crm_user_id, field_mapping=mapping_dict
        )
        return Metrics(
            quota=metrics.quota,
            closed_won=metrics.closed_won,
            pipeline=metrics.pipeline,
            forecast=metrics.forecast,
        )
