from datetime import datetime

from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app.common.oauth.flow_manager import OAuthFlowType
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationConfigError,
    IntegrationCredentialsError,
    IntegrationTokenNotFoundError,
)
from app.workspace.models import IntegrationConfig
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.schemas import OrgEnvironment, SalesforceCredentials
from app.workspace.services.base.oauth_connection import BaseOAuthConnection


class SalesforceTokenResponse(BaseModel):
    external_user_id: str
    external_org_id: str
    instance_url: str | None = None
    access_token: str
    expires_at: datetime


class SalesforceConnectionService(
    BaseOAuthConnection[SalesforceCredentials, SalesforceTokenResponse]
):
    def __init__(
        self,
        db_session: AsyncSession,
        integration_user_repo: IntegrationUserRepository,
        integration_cfg_repo: IntegrationConfigRepository,
        auth_url: str,
        token_url: str,
        redirect_uri: str,
        flow_type: OAuthFlowType,  # todo: replace with use_pkce
    ):
        super().__init__(
            db_session=db_session,
            integration_user_repo=integration_user_repo,
            integration_cfg_repo=integration_cfg_repo,
            auth_url=auth_url,
            token_url=token_url,
            redirect_uri=redirect_uri,
            flow_type=flow_type,
        )

    @property
    def integration_source(self) -> IntegrationSource:
        return IntegrationSource.SALESFORCE

    @property
    def default_scope(self) -> str:
        return "api refresh_token"

    @property
    def default_token_expiry_seconds(self) -> int:
        return 7200  # 2 hours for Salesforce tokens

    async def _get_config_and_credentials(
        self, environment: OrgEnvironment
    ) -> tuple[IntegrationConfig, SalesforceCredentials]:
        integration_config = await self.integration_cfg_repo.get_by_org_and_source(
            org_id=environment.organization_id, source=self.integration_source
        )

        if not integration_config:
            raise IntegrationConfigError(
                f"No Salesforce integration config found for organization {environment.organization_id}"
            )

        return integration_config, SalesforceCredentials.model_validate(
            integration_config.credentials
        )

    def _validate_credentials(self, credentials: SalesforceCredentials) -> None:
        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError(
                "Missing Salesforce client_id and client_secret"
            )

    async def _extract_user_info_from_token(
        self, token_data: dict, _: SalesforceCredentials
    ) -> dict[str, str]:
        if not token_data.get("id"):
            raise IntegrationTokenNotFoundError("Missing user information")

        salesforce_user_id = token_data["id"].split("/")[-1]
        salesforce_org_id = token_data["id"].split("/")[-2]
        return {
            "external_user_id": salesforce_user_id,
            "external_org_id": salesforce_org_id,
        }

    def _get_redirect_uri_for_token_exchange(self) -> str:
        return self.redirect_uri

    def _create_token_response(
        self, integration_user, expires_at: datetime
    ) -> SalesforceTokenResponse:
        return SalesforceTokenResponse(
            external_user_id=integration_user.external_user_id,
            external_org_id=integration_user.external_org_id,
            access_token=integration_user.access_token,
            instance_url=integration_user.instance_url,
            expires_at=expires_at,
        )
