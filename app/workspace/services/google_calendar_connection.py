from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.oauth.flow_manager import OAuthFlowType
from app.integrations.adapters.google_calendar.client import (
    GoogleCalendarClient,
)
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationConfigError,
    IntegrationCredentialsError,
)
from app.workspace.models import IntegrationConfig
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.schemas import (
    GoogleCalendarCredentials,
    GoogleCalendarTokenResponse,
    OrgEnvironment,
)
from app.workspace.services.base.oauth_connection import BaseOAuthConnection


class GoogleCalendarConnectionService(
    BaseOAuthConnection[GoogleCalendarCredentials, GoogleCalendarTokenResponse]
):
    def __init__(
        self,
        db_session: AsyncSession,
        integration_user_repo: IntegrationUserRepository,
        integration_cfg_repo: IntegrationConfigRepository,
        auth_url: str,
        token_url: str,
        redirect_uri: str,
        flow_type: OAuthFlowType,
    ):
        super().__init__(
            db_session=db_session,
            integration_user_repo=integration_user_repo,
            integration_cfg_repo=integration_cfg_repo,
            auth_url=auth_url,
            token_url=token_url,
            redirect_uri=redirect_uri,
            flow_type=flow_type,
        )

    @property
    def integration_source(self) -> IntegrationSource:
        return IntegrationSource.GOOGLE_CALENDAR

    @property
    def default_scope(self) -> str:
        return "https://www.googleapis.com/auth/calendar"

    @property
    def default_token_expiry_seconds(self) -> int:
        return 3600

    async def _get_config_and_credentials(
        self, environment: OrgEnvironment
    ) -> tuple[IntegrationConfig, GoogleCalendarCredentials]:
        integration_config = await self.integration_cfg_repo.get_by_org_and_source(
            org_id=environment.organization_id, source=self.integration_source
        )

        if not integration_config:
            raise IntegrationConfigError(
                f"No Google Calendar integration config found for organization {environment.organization_id}"
            )

        return integration_config, GoogleCalendarCredentials.model_validate(
            integration_config.credentials
        )

    def _validate_credentials(self, credentials: GoogleCalendarCredentials) -> None:
        if not credentials.client_id or not credentials.client_secret:
            raise IntegrationCredentialsError(
                "Missing Google Calendar client_id and client_secret"
            )

    async def _extract_user_info_from_token(
        self, token_data: dict, credentials: GoogleCalendarCredentials
    ) -> dict[str, str]:
        calendar_client = GoogleCalendarClient(
            credentials={
                "access_token": token_data.get("access_token"),
                "refresh_token": token_data.get("refresh_token"),
                "client_id": credentials.client_id,
                "client_secret": credentials.client_secret,
                "token_uri": self.token_url,
                "scopes": ["https://www.googleapis.com/auth/calendar"],
            }
        )

        user_info = await calendar_client.get_user_info()
        return {
            "external_user_id": user_info.get("id", "unknown"),
        }

    def _get_redirect_uri_for_token_exchange(self) -> str:
        # weirdest shit ever but Google requires it in popup mode
        return "postmessage"

    def _create_token_response(
        self, integration_user, expires_at: datetime
    ) -> GoogleCalendarTokenResponse:
        return GoogleCalendarTokenResponse(
            external_user_id=integration_user.external_user_id,
            access_token=integration_user.access_token,
            expires_at=expires_at,
        )
