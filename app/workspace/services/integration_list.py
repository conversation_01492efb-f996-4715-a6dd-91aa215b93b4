from uuid import <PERSON>UID

from app.integrations.types import IntegrationSource
from app.workspace.schemas import OrgEnvironment
from app.workspace.schemas.integration import IntegrationInfo, IntegrationsListResponse
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.types import SOURCE_TYPE_MAP


class IntegrationListService:
    def __init__(self, integration_config_service: IntegrationConfigService):
        self.integration_config_service = integration_config_service

    async def get_integrations_list(
        self, user_id: UUID, environment: OrgEnvironment
    ) -> IntegrationsListResponse:
        """Get list of integrations split between active and available"""
        configured_integrations = (
            await self.integration_config_service.get_integration_configs(environment)
        )

        user_active_integrations = []
        for config in configured_integrations:
            integration_user = (
                await self.integration_config_service.get_integration_user(
                    config.id, user_id
                )
            )
            if integration_user:
                user_active_integrations.append(config)

        active_sources = {config.source for config in user_active_integrations}

        integration_metadata = self._get_integration_metadata()

        active_integrations = []
        available_integrations = []

        for config in configured_integrations:
            if config.source not in integration_metadata:
                continue

            metadata = integration_metadata[config.source]
            integration_type = SOURCE_TYPE_MAP[config.source]

            integration_info = IntegrationInfo(
                id=metadata["id"],
                source=config.source,
                integration_type=integration_type,
                name=metadata["name"],
                description=metadata["description"],
                is_active=config.source in active_sources,
            )

            if config.source in active_sources:
                active_integrations.append(integration_info)
            else:
                available_integrations.append(integration_info)

        return IntegrationsListResponse(
            active_integrations=active_integrations,
            available_integrations=available_integrations,
        )

    def _get_integration_metadata(self) -> dict[IntegrationSource, dict[str, str]]:
        """Get metadata for each integration source"""
        return {
            IntegrationSource.SALESFORCE: {
                "id": "salesforce",
                "name": "Salesforce",
                "description": "Connect your Salesforce CRM to sync accounts and opportunities",
            },
            IntegrationSource.HUBSPOT: {
                "id": "hubspot",
                "name": "HubSpot",
                "description": "Sync your HubSpot contacts and deals with Pearl",
            },
            IntegrationSource.SLACK: {
                "id": "slack",
                "name": "Slack",
                "description": "Connect your Slack workspace",
            },
            IntegrationSource.TEAMS: {
                "id": "teams",
                "name": "Microsoft Teams",
                "description": "Connect your Microsoft Teams workspace",
            },
            IntegrationSource.GOOGLE_CALENDAR: {
                "id": "google-calendar",
                "name": "Google Calendar",
                "description": "Sync your Google Calendar events and meetings",
            },
            IntegrationSource.OUTLOOK_CALENDAR: {
                "id": "outlook-calendar",
                "name": "Outlook Calendar",
                "description": "Connect your Microsoft Outlook calendar",
            },
        }
