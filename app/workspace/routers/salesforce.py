from fastapi import APIRouter, Query

from app.auth.dependencies import AuthenticatedUserIdDep
from app.common.helpers.logger import get_logger
from app.workspace.dependencies import (
    SalesforceConnectionServiceDep,
    UserEnvDep,
)

logger = get_logger()

router = APIRouter()


@router.get("/salesforce/auth-url")
async def get_salesforce_auth_url(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: SalesforceConnectionServiceDep,
):
    oauth_uri = await service.generate_oauth_authorization_uri(
        user_id=user_id,
        environment=user_env,
    )

    return {"auth_url": oauth_uri}


@router.get("/salesforce/callback")
async def process_salesforce_callback(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: SalesforceConnectionServiceDep,
    code: str = Query(...),
    state: str = Query(...),
):
    crm_token = await service.process_oauth_callback(
        user_id=user_id,
        environment=user_env,
        code=code,
        state=state,
    )

    return {
        "message": "OAuth succeed",
        "crm_user_id": crm_token.external_user_id,
        "user_id": str(user_id),
    }
