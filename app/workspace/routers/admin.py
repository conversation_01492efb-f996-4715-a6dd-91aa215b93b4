from fastapi import APIRouter, Depends

from app.workspace.dependencies import (
    OrganizationTeamServiceDep,
    UserOrgIdDep,
    require_admin,
)
from app.workspace.schemas import (
    OrganizationMemberProfileList,
)

"""
Router for admin-only endpoints
"""
router = APIRouter(prefix="/admin", dependencies=[Depends(require_admin)])


@router.get(
    "/member_profiles",
    response_model=OrganizationMemberProfileList,
    name="get_member_profiles",
)
async def get_member_profiles(
    org_id: UserOrgIdDep, service: OrganizationTeamServiceDep
):
    members = await service.get_team_member_profiles(org_id)
    return OrganizationMemberProfileList(members=members)
