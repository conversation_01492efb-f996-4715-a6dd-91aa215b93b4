import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import BaseModel
from app.workspace.models import (
    Environment,
    OrganizationMember,
)


class OrganizationMemberThread(BaseModel):
    __tablename__ = "organization_member_threads"

    thread_id: Mapped[str] = mapped_column(
        Text, nullable=False, index=True, unique=True
    )
    thread_name: Mapped[str] = mapped_column(String(255), nullable=True)
    organization_member_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("organization_member.id"),
        nullable=False,
        index=True,
    )
    environment_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("environment.id"),
        nullable=False,
    )
    crm_account_id: Mapped[str | None] = mapped_column(String(50), nullable=True)

    organization_member: Mapped[OrganizationMember] = relationship("OrganizationMember")
    environment: Mapped[Environment] = relationship("Environment")

    def __repr__(self):
        return (
            f"<OrganizationMemberThread(id='{self.id}', thread_id='{self.thread_id}')>"
        )

    __table_args__ = (
        UniqueConstraint(
            "thread_id",
            "organization_member_id",
            "environment_id",
            name="uq_thread_organization_member_environment",
        ),
    )


class ActionPrompt(BaseModel):
    """Model for action prompts with simple versioning"""

    __tablename__ = "action_prompts"

    title: Mapped[str] = mapped_column(
        String(200), nullable=False, unique=True, index=True
    )
    description: Mapped[str] = mapped_column(Text, nullable=False)
    prompt_content: Mapped[str] = mapped_column(Text, nullable=False)
    version: Mapped[int] = mapped_column(Integer, default=1)
    is_active: Mapped[bool] = mapped_column(default=True)

    def __repr__(self):
        return f"<ActionPrompt(id='{self.id}', title='{self.title}', version={self.version})>"
