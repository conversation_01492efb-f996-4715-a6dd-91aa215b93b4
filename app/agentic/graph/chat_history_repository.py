from typing import Literal

from langchain_core.messages import AIMessage, HumanMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.base import BaseCheckpointSaver

from app.agentic.schemas import (
    PaginationInfo,
    ThreadHistoryResponse,
    ThreadMessage,
)


class ChatHistoryRepository:
    def __init__(self, checkpointer: BaseCheckpointSaver):
        self.checkpointer = checkpointer

    async def get_historical_messages(
        self, thread_id: str, page: int, size: int
    ) -> ThreadHistoryResponse:
        all_langchain_messages = await self._get_latest_checkpoint_messages(thread_id)

        total_messages = len(all_langchain_messages)
        total_pages = (total_messages + size - 1) // size

        pagination_info = PaginationInfo(
            thread_id=thread_id,
            current_page=page,
            page_size=size,
            total_messages=total_messages,
            total_pages=total_pages,
        )

        paginated_messages = self._get_paginated_messages(
            all_langchain_messages, page, size
        )

        historical_messages = [
            message
            for lc_message in paginated_messages
            if (message := self._parse_historical_message_data(lc_message)) is not None
        ]

        return ThreadHistoryResponse(
            pagination=pagination_info,
            messages=historical_messages,
        )

    async def _get_latest_checkpoint_messages(
        self, thread_id: str
    ) -> list[AIMessage | HumanMessage | ToolMessage]:
        config = RunnableConfig(configurable={"thread_id": thread_id})

        latest_checkpoint_tuple = await anext(
            self.checkpointer.alist(config=config, limit=1), None
        )

        if latest_checkpoint_tuple is None:
            return []

        checkpoint_content = latest_checkpoint_tuple.checkpoint
        channel_values = checkpoint_content.get("channel_values", {})
        return channel_values.get("messages", [])

    @staticmethod
    def _get_paginated_messages(
        all_messages: list[AIMessage | HumanMessage | ToolMessage],
        page: int,
        size: int,
    ) -> list[AIMessage | HumanMessage | ToolMessage]:
        start_index = (page - 1) * size
        end_index = start_index + size
        return all_messages[start_index:end_index]

    @staticmethod
    def _parse_historical_message_data(
        lc_message: AIMessage | HumanMessage | ToolMessage,
    ) -> ThreadMessage | None:
        if not hasattr(lc_message, "content"):
            return None

        content = str(lc_message.content)

        if not content.strip():
            return None

        # TODO: serialize tool calls/messages properly
        role: Literal["user", "assistant"]
        if isinstance(lc_message, HumanMessage):
            role = "user"
        elif isinstance(lc_message, AIMessage):
            role = "assistant"
        else:
            return None

        if lc_message.id is None:
            return None

        return ThreadMessage(id=lc_message.id, role=role, content=content)
