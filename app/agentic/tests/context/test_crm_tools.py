from uuid import uuid4

import pytest

from app.agentic.context.crm_tools import CRMToolBuilder, _Delegator
from app.agentic.context.schemas import (
    ToolDefinition,
)


@pytest.fixture
def delegator(user_id):
    environment_id = uuid4()
    return _Delegator(user_id, environment_id)


@pytest.mark.anyio
async def test_user_crm_delegator_init(user_id):
    environment_id = uuid4()
    delegator = _Delegator(user_id, environment_id)

    assert delegator.user_id == user_id
    assert delegator.environment_id == environment_id


@pytest.mark.anyio
async def test_execute_crm_method(mocker, delegator, mock_crm_provider):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return mock_crm_provider

    mock_user_integrations.crm = mock_crm

    mock_create_user_integrations = mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_crm_provider.get_opportunity = mocker.AsyncMock(
        return_value={"Id": "001", "Name": "Test Opp"}
    )

    result = await delegator._execute_crm_method("get_opportunity", "001")

    assert result == {"Id": "001", "Name": "Test Opp"}

    mock_create_user_integrations.assert_called_once_with(
        user_id=delegator.user_id,
        environment_id=delegator.environment_id,
        db_session=mock_session,
    )
    mock_crm_provider.get_opportunity.assert_called_once_with("001")


@pytest.mark.anyio
async def test_execute_crm_method_no_integration(mocker, delegator):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return None

    mock_user_integrations.crm = mock_crm

    mock_create_user_integrations = mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    with pytest.raises(
        RuntimeError, match=f"No CRM integration for user {delegator.user_id}"
    ):
        await delegator._execute_crm_method("get_opportunity", "001")

    mock_create_user_integrations.assert_called_once_with(
        user_id=delegator.user_id,
        environment_id=delegator.environment_id,
        db_session=mock_session,
    )


@pytest.mark.anyio
async def test_get_current_user_territory(mocker, delegator):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_accounts = [
        {"id": "acc_1", "name": "Territory Account 1", "territory": "North"},
        {"id": "acc_2", "name": "Territory Account 2", "territory": "South"},
    ]

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(
        return_value=mock_accounts
    )

    mock_create_user_integrations = mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    result = await delegator.get_current_user_territory()

    assert result == mock_accounts

    mock_create_user_integrations.assert_called_once_with(
        user_id=delegator.user_id,
        environment_id=delegator.environment_id,
        db_session=mock_session,
    )


@pytest.mark.anyio
async def test_get_current_user_territory_empty(mocker, delegator):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(return_value=[])

    mock_create_user_integrations = mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    result = await delegator.get_current_user_territory()

    assert result == []

    mock_create_user_integrations.assert_called_once_with(
        user_id=delegator.user_id,
        environment_id=delegator.environment_id,
        db_session=mock_session,
    )


@pytest.mark.anyio
async def test_getattr_dynamic_methods(mocker, delegator, mock_crm_provider):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return mock_crm_provider

    mock_user_integrations.crm = mock_crm

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_crm_provider.get_opportunity = mocker.AsyncMock(
        return_value={"Id": "001", "Name": "Test Opp"}
    )
    mock_crm_provider.update_opportunity = mocker.AsyncMock(
        return_value={"Id": "001", "Name": "Updated Opp"}
    )
    mock_crm_provider.get_account = mocker.AsyncMock(
        return_value={"Id": "002", "Name": "Test Account"}
    )
    mock_crm_provider.create_contact = mocker.AsyncMock(
        return_value={"Id": "003", "Name": "New Contact"}
    )

    result1 = await delegator.get_opportunity("001")
    assert result1 == {"Id": "001", "Name": "Test Opp"}
    mock_crm_provider.get_opportunity.assert_called_with("001")

    result2 = await delegator.update_opportunity("001", {"Name": "Updated Opp"})
    assert result2 == {"Id": "001", "Name": "Updated Opp"}
    mock_crm_provider.update_opportunity.assert_called_with(
        "001", {"Name": "Updated Opp"}
    )

    result3 = await delegator.get_account("002")
    assert result3 == {"Id": "002", "Name": "Test Account"}
    mock_crm_provider.get_account.assert_called_with("002")

    result4 = await delegator.create_contact({"Name": "New Contact"})
    assert result4 == {"Id": "003", "Name": "New Contact"}
    mock_crm_provider.create_contact.assert_called_with({"Name": "New Contact"})


@pytest.mark.anyio
async def test_getattr_with_args_and_kwargs(mocker, delegator, mock_crm_provider):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return mock_crm_provider

    mock_user_integrations.crm = mock_crm

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_crm_provider.search_opportunities = mocker.AsyncMock(
        return_value=[{"Id": "001", "Name": "Found Opp"}]
    )

    criteria = {"Name": "Test"}
    limit = 10
    result = await delegator.search_opportunities(criteria, limit=limit, sort="Name")

    assert result == [{"Id": "001", "Name": "Found Opp"}]
    mock_crm_provider.search_opportunities.assert_called_with(
        criteria, limit=10, sort="Name"
    )


@pytest.mark.anyio
async def test_multiple_concurrent_calls(mocker, delegator, mock_crm_provider):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return mock_crm_provider

    mock_user_integrations.crm = mock_crm

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_crm_provider.get_opportunity = mocker.AsyncMock(
        return_value={"Id": "001", "Name": "Opp"}
    )
    mock_crm_provider.get_account = mocker.AsyncMock(
        return_value={"Id": "002", "Name": "Account"}
    )
    mock_crm_provider.get_contact = mocker.AsyncMock(
        return_value={"Id": "003", "Name": "Contact"}
    )

    import asyncio

    results = await asyncio.gather(
        delegator.get_opportunity("001"),
        delegator.get_account("002"),
        delegator.get_contact("003"),
    )

    assert results[0] == {"Id": "001", "Name": "Opp"}
    assert results[1] == {"Id": "002", "Name": "Account"}
    assert results[2] == {"Id": "003", "Name": "Contact"}

    mock_crm_provider.get_opportunity.assert_called_with("001")
    mock_crm_provider.get_account.assert_called_with("002")
    mock_crm_provider.get_contact.assert_called_with("003")


@pytest.mark.anyio
async def test_crm_tool_builder_init(user_id, mock_user_integrations_instance):
    builder = CRMToolBuilder(user_id, mock_user_integrations_instance)

    assert builder.user_id == user_id
    assert builder.user_integrations == mock_user_integrations_instance
    assert builder.environment_id == mock_user_integrations_instance.environment.id


@pytest.mark.anyio
async def test_crm_tool_builder_build_tools(user_id, mock_user_integrations_instance):
    builder = CRMToolBuilder(user_id, mock_user_integrations_instance)

    tools = await builder.build_tools()

    assert len(tools) == len(CRMToolBuilder.CRM_METHODS)

    expected_tool_names = [
        method_name for method_name, _, _, _ in CRMToolBuilder.CRM_METHODS
    ]
    actual_tool_names = [tool.name for tool in tools]

    for expected_name in expected_tool_names:
        assert expected_name in actual_tool_names

    for tool in tools:
        assert isinstance(tool, ToolDefinition)
        assert tool.name in expected_tool_names
        assert tool.description is not None
        assert tool.args_schema is not None
        assert tool.coroutine is not None
        assert tool.requires_human_review is not None

        method_details = next(
            (
                details
                for details in CRMToolBuilder.CRM_METHODS
                if details[0] == tool.name
            ),
            None,
        )
        assert method_details is not None

        _, expected_description, expected_schema, _ = method_details
        assert tool.description == expected_description
        assert tool.args_schema == expected_schema


@pytest.mark.anyio
async def test_crm_tool_builder_tool_execution(
    mocker, user_id, mock_user_integrations_instance, mock_crm_provider
):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return mock_crm_provider

    mock_user_integrations.crm = mock_crm

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_crm_provider.get_opportunity = mocker.AsyncMock(
        return_value={"Id": "001", "Name": "Test Opportunity"}
    )
    mock_crm_provider.update_contact = mocker.AsyncMock(
        return_value={"Id": "003", "Name": "Updated Contact"}
    )

    builder = CRMToolBuilder(user_id, mock_user_integrations_instance)
    tools = await builder.build_tools()

    tool_map = {tool.name: tool for tool in tools}

    get_opp_tool = tool_map["get_opportunity"]
    result = await get_opp_tool.coroutine("001")
    assert result == {"Id": "001", "Name": "Test Opportunity"}
    mock_crm_provider.get_opportunity.assert_called_with("001")

    update_contact_tool = tool_map["update_contact"]
    update_data = {"Name": "Updated Contact"}
    result = await update_contact_tool.coroutine("003", update_data)
    assert result == {"Id": "003", "Name": "Updated Contact"}
    mock_crm_provider.update_contact.assert_called_with("003", update_data)
