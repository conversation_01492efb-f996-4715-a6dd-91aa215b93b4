import datetime
from unittest.mock import AsyncMock, MagicMock
from uuid import uuid4

import pytest

from app.agentic.context.calendar_tools import CalendarToolBuilder


@pytest.fixture
def mock_user_integrations():
    mock_integrations = MagicMock()
    mock_environment = MagicMock()
    mock_environment.id = uuid4()
    mock_integrations.environment = mock_environment
    return mock_integrations


@pytest.fixture
def calendar_tool_builder(mock_user_integrations):
    user_id = uuid4()
    return CalendarToolBuilder(user_id, mock_user_integrations)


@pytest.mark.anyio
async def test_calendar_tool_builder_build_tools(calendar_tool_builder):
    tools = await calendar_tool_builder.build_tools()

    assert len(tools) == 1
    tool = tools[0]

    assert tool.name == "get_upcoming_week_calendar_events"
    assert "upcoming calendar events" in tool.description.lower()
    assert tool.requires_human_review is False
    assert tool.args_schema.__name__ == "GetUpcomingCalendarEvents"


@pytest.mark.anyio
async def test_get_upcoming_week_calendar_events_with_primary_calendar(
    calendar_tool_builder, monkeypatch
):
    mock_session = AsyncMock()
    mock_create_user_integrations = AsyncMock()
    mock_user_integrations = AsyncMock()
    mock_calendar = AsyncMock()

    primary_calendar = {
        "id": "<EMAIL>",
        "name": "Primary Calendar",
        "is_primary": True,
        "access_role": "owner",
    }

    now = datetime.datetime.now(datetime.UTC)
    mock_event = {
        "id": "event1",
        "calendar_id": "<EMAIL>",
        "title": "Test Meeting",
        "description": "A test meeting",
        "location": "Conference Room A",
        "start": {
            "date_time": (now + datetime.timedelta(hours=1)).isoformat(),
            "day": None,
            "timezone": "UTC",
        },
        "end": {
            "date_time": (now + datetime.timedelta(hours=2)).isoformat(),
            "day": None,
            "timezone": "UTC",
        },
        "all_day": False,
        "attendees": [],
        "status": "confirmed",
    }

    mock_calendar.list_calendars.return_value = [primary_calendar]
    mock_calendar.list_events.return_value = {
        "events": [mock_event],
        "next_page_token": None,
    }
    mock_user_integrations.calendar.return_value = mock_calendar
    mock_create_user_integrations.return_value = mock_user_integrations

    monkeypatch.setattr(
        "app.agentic.context.calendar_tools.AsyncSessionLocal", lambda: mock_session
    )
    monkeypatch.setattr(
        "app.agentic.context.calendar_tools.create_user_integrations",
        mock_create_user_integrations,
    )

    tools = await calendar_tool_builder.build_tools()
    get_events_tool = tools[0]

    result = await get_events_tool.coroutine()

    assert result["calendar_id"] == "<EMAIL>"
    assert result["total_events"] == 1
    assert len(result["events"]) == 1

    event = result["events"][0]
    assert event["id"] == "event1"
    assert event["title"] == "Test Meeting"
    assert event["description"] == "A test meeting"
    assert event["location"] == "Conference Room A"
    assert event["all_day"] is False
    assert event["status"] == "confirmed"

    start_time = datetime.datetime.fromisoformat(
        result["time_range"]["start"].replace("Z", "+00:00")
    )
    end_time = datetime.datetime.fromisoformat(
        result["time_range"]["end"].replace("Z", "+00:00")
    )
    time_diff = end_time - start_time
    assert 6.9 <= time_diff.days <= 7.1  # Allow for small timing differences


@pytest.mark.anyio
async def test_get_upcoming_week_calendar_events_no_calendar_integration(
    calendar_tool_builder, monkeypatch
):
    mock_session = AsyncMock()
    mock_create_user_integrations = AsyncMock()
    mock_user_integrations = AsyncMock()

    mock_user_integrations.calendar.return_value = None
    mock_create_user_integrations.return_value = mock_user_integrations

    monkeypatch.setattr(
        "app.agentic.context.calendar_tools.AsyncSessionLocal", lambda: mock_session
    )
    monkeypatch.setattr(
        "app.agentic.context.calendar_tools.create_user_integrations",
        mock_create_user_integrations,
    )

    tools = await calendar_tool_builder.build_tools()
    get_events_tool = tools[0]

    with pytest.raises(RuntimeError, match="No calendar integration for user"):
        await get_events_tool.coroutine()


@pytest.mark.anyio
async def test_get_upcoming_week_calendar_events_with_specific_calendar_id(
    calendar_tool_builder, monkeypatch
):
    mock_session = AsyncMock()
    mock_create_user_integrations = AsyncMock()
    mock_user_integrations = AsyncMock()
    mock_calendar = AsyncMock()

    mock_calendar.list_events.return_value = {
        "events": [],
        "next_page_token": None,
    }
    mock_user_integrations.calendar.return_value = mock_calendar
    mock_create_user_integrations.return_value = mock_user_integrations

    monkeypatch.setattr(
        "app.agentic.context.calendar_tools.AsyncSessionLocal", lambda: mock_session
    )
    monkeypatch.setattr(
        "app.agentic.context.calendar_tools.create_user_integrations",
        mock_create_user_integrations,
    )

    tools = await calendar_tool_builder.build_tools()
    get_events_tool = tools[0]

    result = await get_events_tool.coroutine(
        calendar_id="<EMAIL>", max_results=25
    )

    assert result["calendar_id"] == "<EMAIL>"
    mock_calendar.list_calendars.assert_not_called()

    mock_calendar.list_events.assert_called_once()
    call_args = mock_calendar.list_events.call_args
    assert call_args[1]["calendar_id"] == "<EMAIL>"
    assert call_args[1]["max_results"] == 25
