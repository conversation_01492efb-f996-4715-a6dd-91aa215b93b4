import pytest

from app.agentic.context.account import get_account_details


@pytest.mark.anyio
async def test_get_account_details(
    user_id,
    mock_user_integrations_instance,
    mock_crm_provider,
    mocker,
):
    crm_account_id = "account123"

    mock_user_integrations_instance.crm = mocker.AsyncMock(
        return_value=mock_crm_provider
    )

    mock_crm_provider.get_account = mocker.AsyncMock(
        return_value={"Id": "002", "Name": "Test Account"}
    )

    account_details = await get_account_details(
        user_id,
        crm_account_id,
        mock_user_integrations_instance,
    )

    mock_crm_provider.get_account.assert_called_once_with(crm_account_id)
    assert account_details == {"Id": "002", "Name": "Test Account"}


@pytest.mark.anyio
async def test_get_account_details_no_integration(
    user_id,
    mock_user_integrations_instance,
    mocker,
):
    crm_account_id = "account123"

    mock_user_integrations_instance.crm = mocker.AsyncMock(return_value=None)

    with pytest.raises(
        RuntimeError,
        match=f"No CRM integration configured for user {user_id}",
    ):
        await get_account_details(
            user_id, crm_account_id, mock_user_integrations_instance
        )
