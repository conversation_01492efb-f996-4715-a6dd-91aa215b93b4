from app.admin import BaseModelView
from app.agentic.models import (
    ActionPrompt,
    OrganizationMemberThread,
)


class _AgenticModelView(BaseModelView):
    category = "Agentic"


class OrganizationMemberThreadAdmin(_AgenticModelView, model=OrganizationMemberThread):
    autodiscover_order = 10
    name = "Organization Member Thread"
    name_plural = "Organization Member Threads"
    column_list = [
        OrganizationMemberThread.thread_id,
        OrganizationMemberThread.organization_member_id,
        OrganizationMemberThread.environment_id,
        OrganizationMemberThread.crm_account_id,
    ]


class ActionPromptAdmin(_AgenticModelView, model=ActionPrompt):
    autodiscover_order = 10
    name = "Action Prompt"
    name_plural = "Action Prompts"
    column_list = [
        ActionPrompt.title,
        ActionPrompt.version,
        ActionPrompt.is_active,
    ]
