import datetime
from uuid import UUID

from app.agentic.context.base import IToolBuilder
from app.agentic.context.schemas import (
    GetUpcomingCalendarEvents,
    ToolDefinition,
)
from app.core.database import AsyncSessionLocal
from app.workspace.integrations.user_integrations import (
    UserIntegrations,
    create_user_integrations,
)


class CalendarToolBuilder(IToolBuilder):
    CALENDAR_METHODS: list[tuple[str, str, type, bool]] = [
        (
            "get_upcoming_week_calendar_events",
            "Get upcoming calendar events for the next week including today. This helps you understand what meetings and events are scheduled.",
            GetUpcomingCalendarEvents,
            False,
        ),
    ]

    def __init__(self, user_id: UUID, user_integrations: UserIntegrations):
        self.user_id = user_id
        self.user_integrations = user_integrations
        self.environment_id = user_integrations.environment.id

    async def build_tools(self) -> list[ToolDefinition]:
        delegator = _Delegator(self.user_id, self.environment_id)

        return [
            ToolDefinition(
                name=method_name,
                coroutine=getattr(delegator, method_name),
                description=description,
                args_schema=schema_class,
                requires_human_review=requires_human_review,
            )
            for method_name, description, schema_class, requires_human_review in self.CALENDAR_METHODS
        ]


class _Delegator:
    def __init__(self, user_id: UUID, environment_id: UUID):
        self.user_id = user_id
        self.environment_id = environment_id

    async def get_upcoming_week_calendar_events(
        self, calendar_id: str | None = None, max_results: int = 50
    ) -> dict:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            calendar = await user_integrations.calendar()
            if not calendar:
                raise RuntimeError(f"No calendar integration for user {self.user_id}")

            now = datetime.datetime.now(datetime.UTC)
            end_time = now + datetime.timedelta(days=7)

            if not calendar_id:
                calendars = await calendar.list_calendars()
                primary_calendar = None
                for cal in calendars:
                    if cal["is_primary"]:
                        primary_calendar = cal
                        break

                if not primary_calendar:
                    if calendars:
                        primary_calendar = calendars[0]
                    else:
                        return {
                            "calendar_id": None,
                            "events": [],
                            "total_events": 0,
                            "message": "No calendars found for this user",
                            "time_range": {
                                "start": now.isoformat(),
                                "end": end_time.isoformat(),
                            },
                        }

                calendar_id = primary_calendar["id"]

            if not calendar_id:
                return {
                    "calendar_id": None,
                    "events": [],
                    "total_events": 0,
                    "message": "No calendars found for this user",
                    "time_range": {
                        "start": now.isoformat(),
                        "end": end_time.isoformat(),
                    },
                }

            events_response = await calendar.list_events(
                calendar_id=calendar_id,
                start_time=now,
                end_time=end_time,
                max_results=max_results,
                single_events=True,
                order_by="startTime",
                show_deleted=False,
            )

            events = events_response["events"]

            return {
                "calendar_id": calendar_id,
                "events": events,
                "total_events": len(events),
                "time_range": {
                    "start": now.isoformat(),
                    "end": end_time.isoformat(),
                },
                "next_page_token": events_response.get("next_page_token"),
            }
