from typing import Literal
from uuid import UUI<PERSON>

from langchain_linkup.search_tool import LinkupSearchInput, LinkupSearchTool

from app.agentic.context.base import IToolBuilder
from app.agentic.context.calendar_tools import Calendar<PERSON>oolBuilder
from app.agentic.context.crm_tools import CRMT<PERSON><PERSON>uilder
from app.agentic.context.schemas import (
    ToolDefinition,
)
from app.core.config import config
from app.workspace.integrations.user_integrations import (
    UserIntegrations,
)


class WebSearchToolBuilder(IToolBuilder):
    def __init__(
        self,
        api_key: str = config.linkup_api_key,
        depth: Literal["standard", "deep"] = "standard",
        output_type: Literal[
            "searchResults", "sourcedAnswer", "structured"
        ] = "searchResults",
    ):
        self.api_key = api_key
        self.depth = depth
        self.output_type = output_type

    async def build_tools(self) -> list[ToolDefinition]:
        linkup_tool = LinkupSearchTool(
            depth=self.depth,
            output_type=self.output_type,
            linkup_api_key=self.api_key,
        )

        async def search_web(query: str) -> str:
            result = linkup_tool.invoke({"query": query})

            if hasattr(result, "results") and result.results:
                search_results = []
                for item in result.results:
                    search_results.append(
                        f"Title: {item.name}\nURL: {item.url}\nContent: {item.content}\n"
                    )
                return "\n\n".join(search_results)
            return "No search results found."

        return [
            ToolDefinition(
                name="search_web",
                coroutine=search_web,
                description=linkup_tool.description,
                args_schema=LinkupSearchInput,
            )
        ]


class ToolRegistry:
    def __init__(self, user_id: UUID, user_integrations: UserIntegrations) -> None:
        self.user_id = user_id
        self.user_integrations = user_integrations
        self.builders: list[IToolBuilder] = []

    def _register_builder(self, builder: IToolBuilder) -> None:
        self.builders.append(builder)

    async def _build_all_tools(self) -> list[ToolDefinition]:
        all_tools = []
        for builder in self.builders:
            tools = await builder.build_tools()
            all_tools.extend(tools)
        return all_tools

    async def get_tools(self) -> list[ToolDefinition]:
        self._register_builder(WebSearchToolBuilder())
        self._register_builder(CRMToolBuilder(self.user_id, self.user_integrations))
        self._register_builder(
            CalendarToolBuilder(self.user_id, self.user_integrations)
        )

        return await self._build_all_tools()
