from typing import Any
from uuid import UUID

from pydantic import BaseModel

from app.agentic.context.base import IToolBuilder
from app.agentic.context.schemas import (
    CreateContact,
    CreateEvent,
    CreateTask,
    GetAccount,
    GetContact,
    GetCurrentUserTerritory,
    GetEvent,
    GetOpportunity,
    GetTask,
    ListContactsByAccount,
    ListEventsByAccount,
    ListEventsByContact,
    ListEventsByOpportunity,
    ListOpportunitiesByAccount,
    ListTasksByAccount,
    ListTasksByContact,
    ListTasksByOpportunity,
    SearchAccounts,
    SearchContacts,
    SearchOpportunities,
    ToolDefinition,
    UpdateAccount,
    UpdateContact,
    UpdateEvent,
    UpdateOpportunity,
    UpdateTask,
)
from app.core.database import AsyncSessionLocal
from app.workspace.integrations.user_integrations import (
    UserIntegrations,
    create_user_integrations,
)


class CRMToolBuilder(IToolBuilder):
    CRM_METHODS: list[tuple[str, str, type[BaseModel], bool]] = [
        (
            "get_opportunity",
            "Fetch a CRM opportunity by its ID. An opportunity represents a potential sale and can contain a lot of details about the deal and the account generally speaking.",
            GetOpportunity,
            False,
        ),
        (
            "update_opportunity",
            "Update a CRM opportunity with provided fields",
            UpdateOpportunity,
            True,
        ),
        (
            "list_opportunities_by_account",
            "List CRM opportunities for a given account. This gives you minimal info about the different opportunities. If you need more details, use the `get_opportunity` tool.",
            ListOpportunitiesByAccount,
            False,
        ),
        (
            "search_opportunities",
            "Search CRM opportunities by criteria",
            SearchOpportunities,
            False,
        ),
        ("get_account", "Fetch a CRM account by its ID", GetAccount, False),
        (
            "update_account",
            "Update a CRM account with provided fields",
            UpdateAccount,
            True,
        ),
        (
            "search_accounts",
            "Search CRM accounts by criteria",
            SearchAccounts,
            False,
        ),
        ("get_contact", "Fetch a CRM contact by its ID", GetContact, False),
        (
            "create_contact",
            "Create a new CRM contact",
            CreateContact,
            True,
        ),
        (
            "update_contact",
            "Update a CRM contact with provided fields",
            UpdateContact,
            True,
        ),
        (
            "list_contacts_by_account",
            "List CRM contacts for a given account",
            ListContactsByAccount,
            False,
        ),
        (
            "search_contacts",
            "Search CRM contacts by criteria",
            SearchContacts,
            False,
        ),
        ("get_task", "Fetch a CRM task by its ID", GetTask, False),
        (
            "create_task",
            "Create a new CRM task",
            CreateTask,
            True,
        ),
        (
            "update_task",
            "Update a CRM task with provided fields",
            UpdateTask,
            True,
        ),
        (
            "list_tasks_by_contact",
            "List CRM tasks for a given contact",
            ListTasksByContact,
            False,
        ),
        (
            "list_tasks_by_account",
            "List CRM tasks for a given account",
            ListTasksByAccount,
            False,
        ),
        ("get_event", "Fetch a CRM event by its ID", GetEvent, False),
        (
            "create_event",
            "Create a new CRM event",
            CreateEvent,
            True,
        ),
        (
            "update_event",
            "Update a CRM event with provided fields",
            UpdateEvent,
            True,
        ),
        (
            "list_events_by_contact",
            "List CRM events for a given contact",
            ListEventsByContact,
            False,
        ),
        (
            "list_events_by_account",
            "List CRM events for a given account",
            ListEventsByAccount,
            False,
        ),
        (
            "list_events_by_opportunity",
            "List CRM events for a given opportunity",
            ListEventsByOpportunity,
            False,
        ),
        (
            "list_tasks_by_opportunity",
            "List CRM tasks for a given opportunity",
            ListTasksByOpportunity,
            False,
        ),
        (
            "get_current_user_territory",
            "Get my sales territory - shows which accounts I have access to",
            GetCurrentUserTerritory,
            False,
        ),
    ]

    def __init__(self, user_id: UUID, user_integrations: UserIntegrations):
        self.user_id = user_id
        self.user_integrations = user_integrations
        self.environment_id = user_integrations.environment.id

    async def build_tools(self) -> list[ToolDefinition]:
        delegator = _Delegator(self.user_id, self.environment_id)

        return [
            ToolDefinition(
                name=method_name,
                coroutine=getattr(delegator, method_name),
                description=description,
                args_schema=schema_class,
                requires_human_review=requires_human_review,
            )
            for method_name, description, schema_class, requires_human_review in self.CRM_METHODS
        ]


class _Delegator:
    def __init__(self, user_id: UUID, environment_id: UUID):
        self.user_id = user_id
        self.environment_id = environment_id

    async def _execute_crm_method(self, method_name: str, *args, **kwargs):
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            crm = await user_integrations.crm()
            if not crm:
                raise RuntimeError(f"No CRM integration for user {self.user_id}")

            method = getattr(crm, method_name)
            return await method(*args, **kwargs)

    async def get_current_user_territory(self) -> list[dict[str, Any]]:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )
            return await user_integrations.get_crm_accounts()

    def __getattr__(self, name: str):
        async def async_method(*args, **kwargs):
            return await self._execute_crm_method(name, *args, **kwargs)

        return async_method
