from typing import Annotated

from fastapi import Depends, Request
from langfuse import <PERSON><PERSON>
from langfuse.langchain import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langgraph.graph.state import CompiledStateGraph

from app.agentic.graph.graph import GraphFactory
from app.agentic.graph.graph_manager import GraphManager
from app.agentic.repository import OrganizationMemberThreadRepository
from app.agentic.service import AgentService
from app.core.config import config
from app.core.database import AsyncDbSessionDep
from app.workspace.dependencies import (
    AuthenticatedUserIdDep,
    OrgMemberIdDep,
    UserEnvDep,
    UserIntegrationsDep,
    UserOrgIdDep,
)


def get_langfuse_client() -> Langfuse:
    return Langfuse(
        public_key=config.langfuse_public_key,
        secret_key=config.langfuse_secret_key,
        host=config.langfuse_host,
    )


LangfuseClientDep = Annotated[Langfuse, Depends(get_langfuse_client)]


def get_langfuse_callback_handler() -> CallbackHandler:
    return CallbackHandler()


LangfuseCallbackHandlerDep = Annotated[
    Callback<PERSON>and<PERSON>, Depends(get_langfuse_callback_handler)
]


async def get_compiled_graph(
    request: Request,
    user_id: AuthenticatedUserIdDep,
    user_integrations: UserIntegrationsDep,
    langfuse_client: LangfuseClientDep,
) -> CompiledStateGraph:
    if not (
        graph_checkpointer := getattr(request.app.state, "graph_checkpointer", None)
    ):
        raise RuntimeError("Graph checkpointer not initialized.")

    graph_definition = await GraphFactory(
        user_id, user_integrations, langfuse_client
    ).create_graph()
    return graph_definition.compile(checkpointer=graph_checkpointer)


CompiledGraphDep = Annotated[CompiledStateGraph, Depends(get_compiled_graph)]


async def get_graph_manager(
    compiled_graph: CompiledGraphDep,
    langfuse_callback_handler: LangfuseCallbackHandlerDep,
) -> GraphManager:
    return GraphManager(
        graph=compiled_graph,
        langfuse_callback_handler=langfuse_callback_handler,
    )


GraphManagerDep = Annotated[GraphManager, Depends(get_graph_manager)]


async def get_agent_service(
    user_id: AuthenticatedUserIdDep,
    org_id: UserOrgIdDep,
    org_member_id: OrgMemberIdDep,
    user_env: UserEnvDep,
    db_session: AsyncDbSessionDep,
    graph_manager: GraphManagerDep,
) -> AgentService:
    organization_member_thread_repository = OrganizationMemberThreadRepository(
        db_session
    )
    return AgentService(
        user_id=user_id,
        org_id=org_id,
        org_member_id=org_member_id,
        environment=user_env,
        db_session=db_session,
        graph_manager=graph_manager,
        organization_member_thread_repository=organization_member_thread_repository,
    )


AgentServiceDep = Annotated[AgentService, Depends(get_agent_service)]
