from sqlalchemy.ext.asyncio import AsyncSession

from app.agentic.action.schemas import ActionListItem
from app.agentic.repository import ActionPromptRepository


class ActionEngine:
    """Engine for providing action prompts based on titles from database"""

    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.repository = ActionPromptRepository(db_session)

    async def get_available_actions(self) -> list[ActionListItem]:
        """Get list of all available action prompts from database"""
        action_prompts = await self.repository.get_all_active()

        actions = []
        for prompt in action_prompts:
            actions.append(
                ActionListItem(
                    title=prompt.title,
                    description=prompt.description,
                    prompt=prompt.prompt_content,
                )
            )

        return actions
