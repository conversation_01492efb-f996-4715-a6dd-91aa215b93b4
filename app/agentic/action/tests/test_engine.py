import pytest

from app.agentic.action.engine import ActionEngine
from app.agentic.action.schemas import ActionListItem
from app.agentic.models import ActionPrompt


@pytest.fixture
def mock_db_session(mocker):
    return mocker.AsyncMock()


@pytest.fixture
def mock_repository(mocker):
    return mocker.AsyncMock()


@pytest.fixture
def action_engine(mock_db_session, mock_repository, mocker):
    mocker.patch(
        "app.agentic.action.engine.ActionPromptRepository",
        return_value=mock_repository,
    )
    return ActionEngine(mock_db_session)


@pytest.mark.anyio
async def test_get_available_actions(action_engine, mock_repository):
    mock_prompts = [
        ActionPrompt(
            id="123",
            title="Prep my next meeting",
            description="Prepare for a sales meeting",
            prompt_content="Meeting prompt content",
            version=1,
            is_active=True,
        ),
        ActionPrompt(
            id="456",
            title="Draft a follow-up email",
            description="Draft a follow-up email",
            prompt_content="Email prompt content",
            version=1,
            is_active=True,
        ),
    ]

    mock_repository.get_all_active.return_value = mock_prompts

    actions = await action_engine.get_available_actions()

    assert len(actions) == 2
    assert all(isinstance(action, ActionListItem) for action in actions)

    titles = [action.title for action in actions]
    assert "Prep my next meeting" in titles
    assert "Draft a follow-up email" in titles

    meeting_action = next(a for a in actions if a.title == "Prep my next meeting")
    assert meeting_action.title == "Prep my next meeting"
    assert meeting_action.description == "Prepare for a sales meeting"
    assert meeting_action.prompt == "Meeting prompt content"
