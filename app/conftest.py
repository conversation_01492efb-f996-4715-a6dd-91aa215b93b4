import os

import pytest
from asgi_lifespan import Lifespan<PERSON>anager
from httpx import ASGITransport, AsyncClient
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import async_sessionmaker, create_async_engine
from sqlalchemy.orm import sessionmaker
from starlette.testclient import TestClient

# This must be done before loading the config
os.environ.setdefault("APP_ENV", "test")

from app.core.config import config
from app.core.database import (
    BaseModel,
    get_async_db_session,
    get_db_session,
)
from app.main import app

# if sqlite is used, add connect_args = {"check_same_thread": False}
engine = create_engine(str(config.database.test_database_url))
TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

with engine.connect() as connection:
    connection.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
    connection.commit()

# Set up the database once
BaseModel.metadata.drop_all(bind=engine)
BaseModel.metadata.create_all(bind=engine)


def transient_db_session():
    # That's because a call to commit always commits the outermost transaction, so we can't nest transactions that way.
    # SQLAlchemy has provided an escape hatch designed to be used test suites:
    # https://docs.sqlalchemy.org/en/20/orm/session_transaction.html#joining-a-session-into-an-external-transaction-such-as-for-test-suites

    connection = engine.connect()

    # begin a non-ORM transaction
    transaction = connection.begin()

    # bind an individual Session to the connection
    db = TestSessionLocal(bind=connection)

    try:
        yield db
    finally:
        db.close()
        if transaction.is_active:
            transaction.rollback()
        connection.close()


app.dependency_overrides[get_db_session] = transient_db_session

test_async_engine = create_async_engine(
    str(config.database.test_async_database_url), pool_pre_ping=True
)
TestAsyncSessionLocal = async_sessionmaker(
    bind=test_async_engine,
    autocommit=False,
    autoflush=False,
)


async def transient_async_db_session():
    async with test_async_engine.connect() as connection:
        transaction = await connection.begin()

        session = TestAsyncSessionLocal(bind=connection)

        try:
            yield session
        finally:
            await session.close()
            if transaction.is_active:
                await transaction.rollback()


app.dependency_overrides[get_async_db_session] = transient_async_db_session


@pytest.fixture
def test_app():
    # allows to remove the import of app.main in app.xxx.tests
    return app


@pytest.fixture
def db_session():
    yield from transient_db_session()


@pytest.fixture
async def async_db_session():
    async for session in transient_async_db_session():
        yield session


@pytest.fixture
async def async_engine():
    return test_async_engine


@pytest.fixture(autouse=True)
async def close_async_engine():
    """
    Ensures that the async SQLAlchemy engine is properly disposed of after each test function.
    This cleanup helps release any open database connections and tasks associated with the event loop,
    avoiding issues with resource leaks or event loop warnings (e.g., "event loop is closed").
    """
    yield
    await test_async_engine.dispose()


@pytest.fixture(autouse=True)
def enforce_test_database_url(monkeypatch):
    test_db_url = str(config.database.test_database_url)
    monkeypatch.setattr(config.database, "database_url", test_db_url)


@pytest.fixture
def client():
    with TestClient(app, raise_server_exceptions=False) as c:
        yield c


@pytest.fixture
async def async_client():
    async with (
        LifespanManager(app),
        AsyncClient(
            transport=ASGITransport(app=app, raise_app_exceptions=False),
            base_url="http://testserver",
        ) as async_c,
    ):
        yield async_c


@pytest.fixture(params=["asyncio"])
def anyio_backend(request):
    return request.param
