import asyncio
from functools import wraps
from typing import Any

from hubspot import <PERSON>b<PERSON><PERSON>
from hubspot.crm.companies.exceptions import ApiException as CompaniesApiException
from hubspot.crm.contacts.exceptions import ApiException as ContactsApiException
from hubspot.crm.deals.exceptions import ApiException as DealsApiException
from hubspot.crm.objects.exceptions import ApiException as ObjectsApiException

from app.common.helpers.logger import get_logger

logger = get_logger()


class HubSpotClientError(Exception):
    """Custom exception for HubSpot client errors"""

    pass


def to_async(func):
    """Decorator to convert sync functions to async"""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, func, *args, **kwargs)

    return wrapper


class HubSpotClient:
    def __init__(
        self,
        access_token: str | None = None,
    ):
        self.access_token = access_token
        self._client = None

    def _create_client(self) -> HubSpot:
        try:
            if self.access_token:
                logger.debug("Creating HubSpot client with access token")
                return HubSpot(access_token=self.access_token)
            else:
                raise ValueError("Access token is required for HubSpot authentication")
        except Exception as e:
            logger.exception("Failed to create HubSpot client")
            raise HubSpotClientError(
                f"Failed to create HubSpot client: {str(e)}"
            ) from e

    @property
    def client(self) -> HubSpot:
        if self._client is None:
            self._client = self._create_client()
        return self._client

    @to_async
    def get_object(
        self, object_type: str, object_id: str, properties: list[str] | None = None
    ) -> dict[str, Any]:
        try:
            if object_type.lower() == "contact":
                result = self.client.crm.contacts.basic_api.get_by_id(
                    contact_id=object_id, properties=properties
                )
            elif object_type.lower() == "company":
                result = self.client.crm.companies.basic_api.get_by_id(
                    company_id=object_id, properties=properties
                )
            elif object_type.lower() == "deal":
                result = self.client.crm.deals.basic_api.get_by_id(
                    deal_id=object_id, properties=properties
                )
            else:
                result = self.client.crm.objects.basic_api.get_by_id(
                    object_type=object_type, object_id=object_id, properties=properties
                )

            return result.to_dict()
        except (
            ContactsApiException,
            CompaniesApiException,
            DealsApiException,
            ObjectsApiException,
        ) as e:
            logger.exception(f"Failed to get {object_type} {object_id}")
            raise HubSpotClientError(f"Failed to get {object_type}: {str(e)}") from e

    @to_async
    def create_object(self, object_type: str, data: dict[str, Any]) -> dict[str, Any]:
        try:
            if object_type.lower() == "contact":
                from hubspot.crm.contacts import SimplePublicObjectInputForCreate

                simple_public_object_input = SimplePublicObjectInputForCreate(
                    properties=data
                )
                result = self.client.crm.contacts.basic_api.create(
                    simple_public_object_input_for_create=simple_public_object_input
                )
            elif object_type.lower() == "company":
                from hubspot.crm.companies import SimplePublicObjectInputForCreate

                simple_public_object_input = SimplePublicObjectInputForCreate(
                    properties=data
                )
                result = self.client.crm.companies.basic_api.create(
                    simple_public_object_input_for_create=simple_public_object_input
                )
            elif object_type.lower() == "deal":
                from hubspot.crm.deals import SimplePublicObjectInputForCreate

                simple_public_object_input = SimplePublicObjectInputForCreate(
                    properties=data
                )
                result = self.client.crm.deals.basic_api.create(
                    simple_public_object_input_for_create=simple_public_object_input
                )
            else:
                from hubspot.crm.objects import SimplePublicObjectInputForCreate

                simple_public_object_input = SimplePublicObjectInputForCreate(
                    properties=data
                )
                result = self.client.crm.objects.basic_api.create(
                    object_type=object_type,
                    simple_public_object_input_for_create=simple_public_object_input,
                )

            return result.to_dict()
        except (
            ContactsApiException,
            CompaniesApiException,
            DealsApiException,
            ObjectsApiException,
        ) as e:
            logger.exception(f"Failed to create {object_type}")
            raise HubSpotClientError(f"Failed to create {object_type}: {str(e)}") from e

    @to_async
    def update_object(
        self, object_type: str, object_id: str, data: dict[str, Any]
    ) -> None:
        try:
            if object_type.lower() == "contact":
                from hubspot.crm.contacts import SimplePublicObjectInput

                simple_public_object_input = SimplePublicObjectInput(properties=data)
                self.client.crm.contacts.basic_api.update(
                    contact_id=object_id,
                    simple_public_object_input=simple_public_object_input,
                )
            elif object_type.lower() == "company":
                from hubspot.crm.companies import SimplePublicObjectInput

                simple_public_object_input = SimplePublicObjectInput(properties=data)
                self.client.crm.companies.basic_api.update(
                    company_id=object_id,
                    simple_public_object_input=simple_public_object_input,
                )
            elif object_type.lower() == "deal":
                from hubspot.crm.deals import SimplePublicObjectInput

                simple_public_object_input = SimplePublicObjectInput(properties=data)
                self.client.crm.deals.basic_api.update(
                    deal_id=object_id,
                    simple_public_object_input=simple_public_object_input,
                )
            else:
                from hubspot.crm.objects import SimplePublicObjectInput

                simple_public_object_input = SimplePublicObjectInput(properties=data)
                self.client.crm.objects.basic_api.update(
                    object_type=object_type,
                    object_id=object_id,
                    simple_public_object_input=simple_public_object_input,
                )
        except (
            ContactsApiException,
            CompaniesApiException,
            DealsApiException,
            ObjectsApiException,
        ) as e:
            logger.exception(f"Failed to update {object_type} {object_id}")
            raise HubSpotClientError(f"Failed to update {object_type}: {str(e)}") from e

    @to_async
    def search_objects(
        self,
        object_type: str,
        filter_groups: list[dict[str, Any]] | None = None,
        properties: list[str] | None = None,
        limit: int = 100,
        after: str | None = None,
    ) -> dict[str, Any]:
        try:
            from hubspot.crm.contacts import PublicObjectSearchRequest

            search_request = PublicObjectSearchRequest(
                filter_groups=filter_groups or [],
                properties=properties,
                limit=limit,
                after=after,
            )

            if object_type.lower() == "contact":
                result = self.client.crm.contacts.search_api.do_search(
                    public_object_search_request=search_request
                )
            elif object_type.lower() == "company":
                result = self.client.crm.companies.search_api.do_search(
                    public_object_search_request=search_request
                )
            elif object_type.lower() == "deal":
                result = self.client.crm.deals.search_api.do_search(
                    public_object_search_request=search_request
                )
            else:
                result = self.client.crm.objects.search_api.do_search(
                    object_type=object_type, public_object_search_request=search_request
                )

            return result.to_dict()
        except (
            ContactsApiException,
            CompaniesApiException,
            DealsApiException,
            ObjectsApiException,
        ) as e:
            logger.exception(f"Failed to search {object_type}")
            raise HubSpotClientError(f"Failed to search {object_type}: {str(e)}") from e

    @to_async
    def list_objects(
        self,
        object_type: str,
        properties: list[str] | None = None,
        limit: int = 100,
        after: str | None = None,
    ) -> dict[str, Any]:
        try:
            if object_type.lower() == "contact":
                result = self.client.crm.contacts.basic_api.get_page(
                    properties=properties, limit=limit, after=after
                )
            elif object_type.lower() == "company":
                result = self.client.crm.companies.basic_api.get_page(
                    properties=properties, limit=limit, after=after
                )
            elif object_type.lower() == "deal":
                result = self.client.crm.deals.basic_api.get_page(
                    properties=properties, limit=limit, after=after
                )
            else:
                result = self.client.crm.objects.basic_api.get_page(
                    object_type=object_type,
                    properties=properties,
                    limit=limit,
                    after=after,
                )

            return result.to_dict()
        except (
            ContactsApiException,
            CompaniesApiException,
            DealsApiException,
            ObjectsApiException,
        ) as e:
            logger.exception(f"Failed to list {object_type}")
            raise HubSpotClientError(f"Failed to list {object_type}: {str(e)}") from e
