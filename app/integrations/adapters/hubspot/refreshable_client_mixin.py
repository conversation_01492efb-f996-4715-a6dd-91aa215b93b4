import functools
from collections.abc import Awaitable, Callable
from typing import Any, TypeVar

from hubspot.crm.contacts.exceptions import ApiException as ContactsApiException
from hubspot.crm.companies.exceptions import ApiException as CompaniesApiException
from hubspot.crm.deals.exceptions import ApiException as DealsApiException
from hubspot.crm.objects.exceptions import ApiException as ObjectsApiException

from app.common.helpers.logger import get_logger
from app.integrations.adapters.hubspot.client import HubSpotClient
from app.integrations.base.credentials_resolver import ICredentials

logger = get_logger()

T = TypeVar("T")


class HubSpotRefreshableClientMixin:
    credentials: ICredentials
    hubspot_client: HubSpotClient

    def init_hubspot_client(self, credentials: ICredentials):
        self.credentials = credentials

        if "access_token" not in self.credentials.secrets:
            error_msg = "Missing required 'access_token' for HubSpot authentication"
            logger.error(error_msg)
            raise ValueError(error_msg)

        client_params = {
            "access_token": self.credentials.secrets["access_token"],
        }
        logger.debug("Using access token authentication for HubSpot")

        self.hubspot_client = HubSpotClient(**client_params)

    async def refresh_hubspot_token(self):
        logger.info("Refreshing HubSpot token")
        self.credentials = await self.credentials.refresh_token()
        logger.info("Re-initializing HubSpot client with refreshed token")
        self.init_hubspot_client(self.credentials)

    @staticmethod
    def handle_expired_session(
        method: Callable[..., Awaitable[T]],
    ) -> Callable[..., Awaitable[T]]:
        @functools.wraps(method)
        async def wrapper(self, *args: Any, **kwargs: Any) -> T:
            try:
                return await method(self, *args, **kwargs)
            except Exception as e:
                current_exception: BaseException | None = e
                while current_exception is not None:
                    # Check for HubSpot authentication errors
                    if isinstance(current_exception, (ContactsApiException, CompaniesApiException, DealsApiException, ObjectsApiException)):
                        # Check if it's an authentication error (401)
                        if hasattr(current_exception, 'status') and current_exception.status == 401:
                            logger.warning(
                                f"Authentication error during {method.__name__}, refreshing token"
                            )
                            await self.refresh_hubspot_token()
                            return await method(self, *args, **kwargs)

                    current_exception = current_exception.__cause__

                raise

        return wrapper
