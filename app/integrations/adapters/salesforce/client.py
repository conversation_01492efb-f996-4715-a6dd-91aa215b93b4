from datetime import datetime, timed<PERSON>ta
from typing import Any, Literal

from simple_salesforce.api import Salesforce
from simple_salesforce.exceptions import SalesforceAuthenticationFailed
from simple_salesforce.format import format_soql

from app.common.helpers.logger import get_logger
from app.common.helpers.to_async import to_async

logger = get_logger()


class SalesforceClientError(Exception):
    pass


class SalesforceClient:
    def __init__(
        self,
        username: str | None = None,
        password: str | None = None,
        security_token: str | None = None,
        instance_url: str | None = None,
        access_token: str | None = None,
        domain: str = "login",
        version: str = "55.0",
    ):
        self.username = username
        self.password = password
        self.security_token = security_token
        self.instance_url = instance_url
        self.access_token = access_token
        self.domain = domain
        self.version = version

    def _create_client(self) -> Salesforce:
        try:
            if self.access_token:
                logger.debug("Creating Salesforce client with access token")
                return Salesforce(
                    instance_url=self.instance_url,
                    session_id=self.access_token,
                    domain=self.domain,
                    version=self.version,
                )
            else:
                logger.debug("Creating Salesforce client with username")
                return Salesforce(
                    username=self.username,
                    password=self.password,
                    security_token=self.security_token,
                    domain=self.domain,
                    version=self.version,
                )

        except SalesforceAuthenticationFailed as e:
            logger.exception("Salesforce authentication failed")
            raise SalesforceClientError(f"Authentication failed: {str(e)}") from e
        except Exception as e:
            logger.exception("Failed to create Salesforce client")
            raise SalesforceClientError(
                f"Client initialization failed: {str(e)}"
            ) from e

    @property
    def _client(self):
        if not hasattr(self, "_lazy_client"):
            self._lazy_client = self._create_client()
        return self._lazy_client

    @to_async
    def get_available_objects(self):
        if not hasattr(self, "_cached_available_objects"):
            desc = self._client.describe()
            self._cached_available_objects = {s["name"] for s in desc["sobjects"]}
        return self._cached_available_objects

    @to_async
    def get_object(self, object_type: str, object_id: str) -> dict[str, Any]:
        try:
            sf_object = getattr(self._client, object_type)
            result = sf_object.get(object_id)
            return result
        except Exception as e:
            logger.exception(f"Failed to get {object_type} {object_id}")
            raise SalesforceClientError(f"Failed to get {object_type}: {str(e)}") from e

    @to_async
    def query(self, soql_query: str) -> dict[str, Any]:
        try:
            return self._client.query(soql_query)
        except Exception as e:
            logger.exception("Query failed")
            raise SalesforceClientError(f"Query failed: {str(e)}") from e

    @to_async
    def create_object(self, object_type: str, data: dict[str, Any]) -> dict[str, Any]:
        try:
            sf_object = getattr(self._client, object_type)
            result = sf_object.create(data)
            return result
        except Exception as e:
            logger.exception(f"Failed to create {object_type}")
            raise SalesforceClientError(
                f"Failed to create {object_type}: {str(e)}"
            ) from e

    @to_async
    def update_object(
        self, object_type: str, object_id: str, data: dict[str, Any]
    ) -> None:
        try:
            sf_object = getattr(self._client, object_type)
            sf_object.update(object_id, data)
        except Exception as e:
            logger.exception(f"Failed to update {object_type} {object_id}")
            raise SalesforceClientError(
                f"Failed to update {object_type}: {str(e)}"
            ) from e

    @to_async
    def delete_object(self, object_type: str, object_id: str) -> None:
        try:
            sf_object = getattr(self._client, object_type)
            sf_object.delete(object_id)
        except Exception as e:
            logger.exception(f"Failed to delete {object_type} {object_id}")
            raise SalesforceClientError(
                f"Failed to delete {object_type}: {str(e)}"
            ) from e

    @to_async
    def list_objects(
        self,
        object_type: str,
        fields: list[str] | Literal["ALL"] | None = None,
        where_clause: str | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        try:
            # Temporary as the agent is not yet aware of the Salesforce instance's data model it lives in and thus the field names.
            def get_select_clause(
                fields: list[str] | Literal["ALL"] | None, object_type: str
            ):
                if fields == "ALL":
                    return "FIELDS(ALL)"
                elif fields:
                    return ", ".join(fields)
                else:
                    if object_type in ["Event", "Task"]:
                        return "Id, Subject, WhatId, WhoId, OwnerId"

                    return "Id, Name"

            select_clause = get_select_clause(fields, object_type)

            base_query = "SELECT {select_fields:literal} FROM {table_name:literal}"
            params: dict[str, object] = {
                "select_fields": select_clause,
                "table_name": object_type,
            }

            if where_clause:
                base_query += " WHERE {where_condition:literal}"
                params["where_condition"] = where_clause

            if order_by:
                base_query += " ORDER BY {order_by:literal}"
                params["order_by"] = order_by

            base_query += " LIMIT {limit} OFFSET {offset}"
            params["limit"] = limit
            params["offset"] = offset

            query = format_soql(base_query, **params)
            result = self._client.query(query)
            return result.get("records", [])
        except ValueError as e:
            logger.exception("Invalid query parameters")
            raise SalesforceClientError(f"Invalid query parameters: {str(e)}") from e
        except Exception as e:
            logger.exception(f"Failed to list {object_type} objects")
            raise SalesforceClientError(
                f"Failed to list {object_type} objects: {str(e)}"
            ) from e

    async def list_objects_by_owner(
        self,
        object_type: str,
        owner_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        additional_filters: str | None = None,
        order_by: str | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        where_clause = f"OwnerId = '{owner_id}'"
        if additional_filters:
            where_clause += f" AND {additional_filters}"

        return await self.list_objects(
            object_type=object_type,
            fields=fields,
            where_clause=where_clause,
            order_by=order_by,
            limit=limit,
            offset=offset,
        )

    async def list_objects_by_ids(
        self,
        object_type: str,
        object_ids: list[str],
        fields: list[str] | Literal["ALL"] | None = None,
    ) -> list[dict[str, Any]]:
        try:
            if not object_ids:
                return []

            formatted_ids = ", ".join(f"'{obj_id}'" for obj_id in object_ids)
            where_clause = f"Id IN ({formatted_ids})"

            return await self.list_objects(
                object_type=object_type,
                fields=fields,
                where_clause=where_clause,
                limit=len(object_ids),
            )
        except Exception as e:
            logger.exception(f"Failed to list {object_type} objects by IDs")
            raise SalesforceClientError(
                f"Failed to list {object_type} objects by IDs: {str(e)}"
            ) from e

    async def list_objects_by_ids_and_owner(
        self,
        object_type: str,
        object_ids: list[str],
        owner_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        additional_filters: str | None = None,
    ) -> list[dict[str, Any]]:
        try:
            if not object_ids:
                return []

            formatted_ids = ", ".join(f"'{obj_id}'" for obj_id in object_ids)
            where_clause = f"Id IN ({formatted_ids}) AND OwnerId = '{owner_id}'"

            if additional_filters:
                where_clause += f" AND {additional_filters}"

            return await self.list_objects(
                object_type=object_type,
                fields=fields,
                where_clause=where_clause,
                limit=len(object_ids),
            )
        except Exception as e:
            logger.exception(f"Failed to list {object_type} objects by IDs and owner")
            raise SalesforceClientError(
                f"Failed to list {object_type} objects by IDs and owner: {str(e)}"
            ) from e

    @to_async
    def get_opportunity_metrics(
        self,
        owner_id: str,
        field_mapping: dict[str, Any] | None = None,
    ) -> dict[str, float]:
        """Get opportunity metrics for a Salesforce user."""
        try:
            mapping = field_mapping or {}
            amount_field = mapping.get("opportunity_amount_field", "Amount")
            stage_field = mapping.get("opportunity_stage_field", "StageName")
            closed_won_pattern = mapping.get("closed_won_stage_pattern", "%Closed Won%")
            closed_lost_pattern = mapping.get(
                "closed_lost_stage_pattern", "%Closed Lost%"
            )
            forecast_multiplier = mapping.get("forecast_probability_multiplier", 0.5)
            metrics_months_limit = mapping.get("metrics_months_limit", 3)

            cutoff_date = datetime.now() - timedelta(days=metrics_months_limit * 30)
            cutoff_date_str = cutoff_date.strftime("%Y-%m-%d")

            def query_sum(stage_pattern=None):
                base_where = (
                    f"OwnerId = '{owner_id}' AND CloseDate >= {cutoff_date_str}"
                )

                if stage_pattern:
                    query = format_soql(
                        "SELECT SUM({amount_field:literal}) total FROM Opportunity WHERE {base_where:literal} AND {stage_field:literal} LIKE {pattern}",
                        amount_field=amount_field,
                        base_where=base_where,
                        stage_field=stage_field,
                        pattern=stage_pattern,
                    )
                else:
                    query = format_soql(
                        "SELECT SUM({amount_field:literal}) total FROM Opportunity WHERE {base_where:literal}",
                        amount_field=amount_field,
                        base_where=base_where,
                    )
                result = self._client.query(query)
                return float(result.get("records", [{}])[0].get("total", 0) or 0)

            closed_won = query_sum(closed_won_pattern)
            total = query_sum()
            closed_lost = query_sum(closed_lost_pattern)

            pipeline = total - closed_won - closed_lost
            forecast = pipeline * forecast_multiplier

            return {
                "closed_won": closed_won,
                "pipeline": pipeline,
                "forecast": forecast,
            }

        except Exception as e:
            logger.exception(f"Failed to get opportunity metrics for user {owner_id}")
            raise SalesforceClientError(
                f"Failed to get opportunity metrics: {str(e)}"
            ) from e

    @to_async
    def get_quota(
        self, owner_id: str, field_mapping: dict[str, Any] | None = None
    ) -> float:
        """Get the most recent quota amount for a Salesforce user from ForecastingQuota table."""
        try:
            mapping = field_mapping or {}
            quota_table = mapping.get("quota_table", "ForecastingQuota")
            quota_amount_field = mapping.get("quota_amount_field", "QuotaAmount")
            quota_owner_field = mapping.get("quota_owner_field", "QuotaOwnerId")

            query = format_soql(
                "SELECT {quota_amount_field:literal} FROM {quota_table:literal} WHERE {quota_owner_field:literal} = {owner_id} ORDER BY LastModifiedDate DESC LIMIT 1",
                quota_amount_field=quota_amount_field,
                quota_table=quota_table,
                quota_owner_field=quota_owner_field,
                owner_id=owner_id,
            )
            result = self._client.query(query)
            records = result.get("records", [])

            if records:
                quota_amount = records[0].get(quota_amount_field)
                if quota_amount is not None:
                    return float(quota_amount)

            return 0.0

        except Exception as e:
            logger.exception(f"Failed to get quota for user {owner_id}")
            raise SalesforceClientError(f"Failed to get quota: {str(e)}") from e
