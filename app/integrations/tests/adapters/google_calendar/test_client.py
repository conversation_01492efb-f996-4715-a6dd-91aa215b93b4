import pytest
from google.auth.exceptions import GoogleAuthError
from googleapiclient.errors import HttpError

from app.integrations.adapters.google_calendar.client import (
    GoogleCalendarClient,
    GoogleCalendarClientError,
)


@pytest.fixture
def mock_credentials():
    return {
        "access_token": "mock_access_token",
        "refresh_token": "mock_refresh_token",
        "client_id": "mock_client_id",
        "client_secret": "mock_client_secret",
        "token_uri": "https://oauth2.googleapis.com/token",
        "scope": "https://www.googleapis.com/auth/calendar",
    }


@pytest.fixture
def mock_http_error():
    def _create_error(status_code, message):
        mock_resp = type(
            "MockResponse",
            (),
            {
                "status": status_code,
                "reason": "Test Error",
                "version": 11,
            },
        )()

        error_content = {"error": {"code": status_code, "message": message}}

        return HttpError(resp=mock_resp, content=str(error_content).encode())

    return _create_error


@pytest.fixture
def mock_minimal_credentials():
    return {
        "access_token": "mock_access_token",
        "refresh_token": "mock_refresh_token",
        "client_id": "mock_client_id",
        "client_secret": "mock_client_secret",
    }


@pytest.fixture
def google_calendar_client(mocker, mock_credentials):
    mock_service = mocker.MagicMock()

    mock_calendar_list = mocker.MagicMock()
    mock_service.calendarList.return_value = mock_calendar_list

    mock_events = mocker.MagicMock()
    mock_service.events.return_value = mock_events

    mock_freebusy = mocker.MagicMock()
    mock_service.freebusy.return_value = mock_freebusy

    mocker.patch(
        "app.integrations.adapters.google_calendar.client.build",
        return_value=mock_service,
    )
    mocker.patch("app.integrations.adapters.google_calendar.client.OAuth2Credentials")

    client = GoogleCalendarClient(credentials=mock_credentials)

    mock_service.reset_mock()
    mock_calendar_list.reset_mock()
    mock_events.reset_mock()
    mock_freebusy.reset_mock()

    return client


def test_create_client_success(mocker, mock_credentials):
    mock_oauth_creds = mocker.MagicMock()
    mock_service = mocker.MagicMock()

    oauth_mock = mocker.patch(
        "app.integrations.adapters.google_calendar.client.OAuth2Credentials",
        return_value=mock_oauth_creds,
    )
    build_mock = mocker.patch(
        "app.integrations.adapters.google_calendar.client.build",
        return_value=mock_service,
    )

    client = GoogleCalendarClient(credentials=mock_credentials)

    oauth_mock.assert_called_once_with(
        token="mock_access_token",  # noqa: S106
        refresh_token="mock_refresh_token",  # noqa: S106
        token_uri="https://oauth2.googleapis.com/token",  # noqa: S106
        client_id="mock_client_id",
        client_secret="mock_client_secret",  # noqa: S106
        scopes=["https://www.googleapis.com/auth/calendar"],
    )
    build_mock.assert_called_once_with("calendar", "v3", credentials=mock_oauth_creds)
    assert client._service == mock_service


def test_create_client_with_minimal_credentials(mocker, mock_minimal_credentials):
    mock_oauth_creds = mocker.MagicMock()
    mock_service = mocker.MagicMock()

    oauth_mock = mocker.patch(
        "app.integrations.adapters.google_calendar.client.OAuth2Credentials",
        return_value=mock_oauth_creds,
    )
    mocker.patch(
        "app.integrations.adapters.google_calendar.client.build",
        return_value=mock_service,
    )

    client = GoogleCalendarClient(credentials=mock_minimal_credentials)

    oauth_mock.assert_called_once_with(
        token="mock_access_token",  # noqa: S106
        refresh_token="mock_refresh_token",  # noqa: S106
        token_uri="https://oauth2.googleapis.com/token",  # noqa: S106
        client_id="mock_client_id",
        client_secret="mock_client_secret",  # noqa: S106
        scopes=[],
    )
    assert client._service == mock_service


def test_create_credentials_failure(mocker, mock_credentials):
    mocker.patch(
        "app.integrations.adapters.google_calendar.client.OAuth2Credentials",
        side_effect=GoogleAuthError("Invalid credentials"),
    )

    with pytest.raises(GoogleCalendarClientError, match="Failed to create credentials"):
        GoogleCalendarClient(credentials=mock_credentials)


def test_build_service_failure(mocker, mock_credentials):
    mock_oauth_creds = mocker.MagicMock()
    mocker.patch(
        "app.integrations.adapters.google_calendar.client.OAuth2Credentials",
        return_value=mock_oauth_creds,
    )
    mocker.patch(
        "app.integrations.adapters.google_calendar.client.build",
        side_effect=Exception("Service build failed"),
    )

    with pytest.raises(
        GoogleCalendarClientError, match="Failed to build Calendar service"
    ):
        GoogleCalendarClient(credentials=mock_credentials)


@pytest.mark.anyio
async def test_list_calendars_success(google_calendar_client):
    expected_result = {
        "kind": "calendar#calendarList",
        "items": [
            {
                "id": "primary",
                "summary": "Test User",
                "primary": True,
            },
            {
                "id": "<EMAIL>",
                "summary": "Test Calendar",
                "primary": False,
            },
        ],
    }

    mock_execute = google_calendar_client._service.calendarList.return_value.list.return_value.execute
    mock_execute.return_value = expected_result

    result = await google_calendar_client.list_calendars()

    assert result == expected_result
    google_calendar_client._service.calendarList.return_value.list.assert_called_once()
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_get_calendar_success(google_calendar_client):
    calendar_id = "<EMAIL>"
    expected_result = {
        "id": calendar_id,
        "summary": "Test Calendar",
        "description": "A test calendar",
    }

    mock_get = google_calendar_client._service.calendarList.return_value.get
    mock_execute = mock_get.return_value.execute
    mock_execute.return_value = expected_result

    result = await google_calendar_client.get_calendar(calendar_id)

    assert result == expected_result
    mock_get.assert_called_once_with(calendarId=calendar_id)
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_list_events_success(google_calendar_client):
    calendar_id = "<EMAIL>"
    expected_result = {
        "kind": "calendar#events",
        "items": [
            {
                "id": "event1",
                "summary": "Test Event 1",
                "start": {"dateTime": "2025-01-01T10:00:00Z"},
                "end": {"dateTime": "2025-01-01T11:00:00Z"},
            },
            {
                "id": "event2",
                "summary": "Test Event 2",
                "start": {"dateTime": "2025-01-01T14:00:00Z"},
                "end": {"dateTime": "2025-01-01T15:00:00Z"},
            },
        ],
    }

    mock_list = google_calendar_client._service.events.return_value.list
    mock_execute = mock_list.return_value.execute
    mock_execute.return_value = expected_result

    result = await google_calendar_client.list_events(
        calendar_id=calendar_id,
        time_min="2025-01-01T00:00:00Z",
        time_max="2025-01-01T23:59:59Z",
        max_results=100,
        single_events=True,
        order_by="startTime",
        show_deleted=False,
        page_token="next_page_token",  # noqa: S106
    )

    assert result == expected_result
    mock_list.assert_called_once_with(
        calendarId=calendar_id,
        timeMin="2025-01-01T00:00:00Z",
        timeMax="2025-01-01T23:59:59Z",
        maxResults=100,
        singleEvents=True,
        orderBy="startTime",
        showDeleted=False,
        pageToken="next_page_token",
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_list_events_minimal_params(google_calendar_client):
    calendar_id = "<EMAIL>"
    expected_result = {"kind": "calendar#events", "items": []}

    mock_list = google_calendar_client._service.events.return_value.list
    mock_execute = mock_list.return_value.execute
    mock_execute.return_value = expected_result

    result = await google_calendar_client.list_events(calendar_id=calendar_id)

    assert result == expected_result
    mock_list.assert_called_once_with(
        calendarId=calendar_id,
        maxResults=250,
        singleEvents=True,
        orderBy="startTime",
        showDeleted=False,
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_get_event_success(google_calendar_client):
    calendar_id = "<EMAIL>"
    event_id = "event123"
    expected_result = {
        "id": event_id,
        "summary": "Test Event",
        "start": {"dateTime": "2025-01-01T10:00:00Z"},
        "end": {"dateTime": "2025-01-01T11:00:00Z"},
    }

    mock_get = google_calendar_client._service.events.return_value.get
    mock_execute = mock_get.return_value.execute
    mock_execute.return_value = expected_result

    result = await google_calendar_client.get_event(calendar_id, event_id)

    assert result == expected_result
    mock_get.assert_called_once_with(calendarId=calendar_id, eventId=event_id)
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_create_event_success(google_calendar_client):
    calendar_id = "<EMAIL>"
    event_data = {
        "summary": "New Event",
        "start": {"dateTime": "2025-01-01T10:00:00Z"},
        "end": {"dateTime": "2025-01-01T11:00:00Z"},
    }
    expected_result = {
        "id": "new_event_123",
        **event_data,
    }

    mock_insert = google_calendar_client._service.events.return_value.insert
    mock_execute = mock_insert.return_value.execute
    mock_execute.return_value = expected_result

    result = await google_calendar_client.create_event(
        calendar_id=calendar_id,
        event_data=event_data,
        send_notifications=True,
    )

    assert result == expected_result
    mock_insert.assert_called_once_with(
        calendarId=calendar_id,
        body=event_data,
        sendNotifications=True,
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_create_event_no_notifications(google_calendar_client):
    calendar_id = "<EMAIL>"
    event_data = {"summary": "New Event"}
    expected_result = {"id": "new_event_123", **event_data}

    mock_insert = google_calendar_client._service.events.return_value.insert
    mock_execute = mock_insert.return_value.execute
    mock_execute.return_value = expected_result

    result = await google_calendar_client.create_event(
        calendar_id=calendar_id,
        event_data=event_data,
        send_notifications=False,
    )

    assert result == expected_result
    mock_insert.assert_called_once_with(
        calendarId=calendar_id,
        body=event_data,
        sendNotifications=False,
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_update_event_success(google_calendar_client):
    calendar_id = "<EMAIL>"
    event_id = "event123"
    event_data = {
        "summary": "Updated Event",
        "start": {"dateTime": "2025-01-01T14:00:00Z"},
        "end": {"dateTime": "2025-01-01T15:00:00Z"},
    }
    expected_result = {
        "id": event_id,
        **event_data,
    }

    mock_update = google_calendar_client._service.events.return_value.update
    mock_execute = mock_update.return_value.execute
    mock_execute.return_value = expected_result

    result = await google_calendar_client.update_event(
        calendar_id=calendar_id,
        event_id=event_id,
        event_data=event_data,
        send_notifications=True,
    )

    assert result == expected_result
    mock_update.assert_called_once_with(
        calendarId=calendar_id,
        eventId=event_id,
        body=event_data,
        sendNotifications=True,
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_update_event_no_notifications(google_calendar_client):
    calendar_id = "<EMAIL>"
    event_id = "event123"
    event_data = {"summary": "Updated Event"}
    expected_result = {"id": event_id, **event_data}

    mock_update = google_calendar_client._service.events.return_value.update
    mock_execute = mock_update.return_value.execute
    mock_execute.return_value = expected_result

    result = await google_calendar_client.update_event(
        calendar_id=calendar_id,
        event_id=event_id,
        event_data=event_data,
        send_notifications=False,
    )

    assert result == expected_result
    mock_update.assert_called_once_with(
        calendarId=calendar_id,
        eventId=event_id,
        body=event_data,
        sendNotifications=False,
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_delete_event_success(google_calendar_client):
    calendar_id = "<EMAIL>"
    event_id = "event123"

    mock_delete = google_calendar_client._service.events.return_value.delete
    mock_execute = mock_delete.return_value.execute
    mock_execute.return_value = None

    result = await google_calendar_client.delete_event(
        calendar_id=calendar_id,
        event_id=event_id,
        send_notifications=True,
    )

    assert result is None
    mock_delete.assert_called_once_with(
        calendarId=calendar_id,
        eventId=event_id,
        sendNotifications=True,
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_delete_event_no_notifications(google_calendar_client):
    calendar_id = "<EMAIL>"
    event_id = "event123"

    mock_delete = google_calendar_client._service.events.return_value.delete
    mock_execute = mock_delete.return_value.execute
    mock_execute.return_value = None

    result = await google_calendar_client.delete_event(
        calendar_id=calendar_id,
        event_id=event_id,
        send_notifications=False,
    )

    assert result is None
    mock_delete.assert_called_once_with(
        calendarId=calendar_id,
        eventId=event_id,
        sendNotifications=False,
    )
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_get_user_info_success(google_calendar_client):
    expected_result = {
        "id": "primary",
        "summary": "<EMAIL>",
        "primary": True,
        "accessRole": "owner",
    }

    mock_get = google_calendar_client._service.calendarList.return_value.get
    mock_execute = mock_get.return_value.execute
    mock_execute.return_value = expected_result

    result = await google_calendar_client.get_user_info()

    assert result == expected_result
    mock_get.assert_called_once_with(calendarId="primary")
    mock_execute.assert_called_once()


@pytest.mark.anyio
async def test_list_calendars_error(google_calendar_client, mock_http_error):
    google_calendar_client._service.calendarList().list().execute.side_effect = (
        mock_http_error(403, "Forbidden")
    )

    with pytest.raises(GoogleCalendarClientError, match="Failed to list calendars"):
        await google_calendar_client.list_calendars()


@pytest.mark.anyio
async def test_get_calendar_error(google_calendar_client, mock_http_error):
    calendar_id = "<EMAIL>"
    google_calendar_client._service.calendarList().get().execute.side_effect = (
        mock_http_error(404, "Not Found")
    )

    with pytest.raises(GoogleCalendarClientError, match="Failed to get calendar"):
        await google_calendar_client.get_calendar(calendar_id)


@pytest.mark.anyio
async def test_list_events_error(google_calendar_client, mock_http_error):
    calendar_id = "<EMAIL>"
    google_calendar_client._service.events().list().execute.side_effect = (
        mock_http_error(400, "Bad Request")
    )

    with pytest.raises(GoogleCalendarClientError, match="Failed to list events"):
        await google_calendar_client.list_events(calendar_id=calendar_id)


@pytest.mark.anyio
async def test_get_event_error(google_calendar_client, mock_http_error):
    calendar_id = "<EMAIL>"
    event_id = "nonexistent_event"
    google_calendar_client._service.events().get().execute.side_effect = (
        mock_http_error(404, "Not Found")
    )

    with pytest.raises(GoogleCalendarClientError, match="Failed to get event"):
        await google_calendar_client.get_event(calendar_id, event_id)


@pytest.mark.anyio
async def test_create_event_error(google_calendar_client, mock_http_error):
    calendar_id = "<EMAIL>"
    event_data = {"summary": "Invalid Event"}
    google_calendar_client._service.events().insert().execute.side_effect = (
        mock_http_error(400, "Invalid event data")
    )

    with pytest.raises(GoogleCalendarClientError, match="Failed to create event"):
        await google_calendar_client.create_event(
            calendar_id=calendar_id, event_data=event_data
        )


@pytest.mark.anyio
async def test_update_event_error(google_calendar_client, mock_http_error):
    calendar_id = "<EMAIL>"
    event_id = "event123"
    event_data = {"summary": "Updated Event"}
    google_calendar_client._service.events().update().execute.side_effect = (
        mock_http_error(403, "Forbidden")
    )

    with pytest.raises(GoogleCalendarClientError, match="Failed to update event"):
        await google_calendar_client.update_event(calendar_id, event_id, event_data)


@pytest.mark.anyio
async def test_delete_event_error(google_calendar_client, mock_http_error):
    calendar_id = "<EMAIL>"
    event_id = "event123"
    google_calendar_client._service.events().delete().execute.side_effect = (
        mock_http_error(410, "Gone")
    )

    with pytest.raises(GoogleCalendarClientError, match="Failed to delete event"):
        await google_calendar_client.delete_event(calendar_id, event_id)


@pytest.mark.anyio
async def test_get_user_info_error(google_calendar_client, mock_http_error):
    google_calendar_client._service.calendarList().get().execute.side_effect = (
        mock_http_error(401, "Unauthorized")
    )

    with pytest.raises(GoogleCalendarClientError, match="Failed to get user info"):
        await google_calendar_client.get_user_info()


@pytest.mark.anyio
async def test_list_calendars_generic_error(google_calendar_client):
    google_calendar_client._service.calendarList().list().execute.side_effect = (
        Exception("Generic error")
    )

    with pytest.raises(
        GoogleCalendarClientError, match="Failed to list calendars: Generic error"
    ):
        await google_calendar_client.list_calendars()


@pytest.mark.anyio
async def test_create_event_generic_error(google_calendar_client):
    calendar_id = "<EMAIL>"
    event_data = {"summary": "Test Event"}
    google_calendar_client._service.events().insert().execute.side_effect = Exception(
        "Connection timeout"
    )

    with pytest.raises(
        GoogleCalendarClientError, match="Failed to create event: Connection timeout"
    ):
        await google_calendar_client.create_event(
            calendar_id=calendar_id, event_data=event_data
        )


def test_create_client_with_empty_credentials(mocker):
    empty_credentials = {}

    oauth_mock = mocker.patch(
        "app.integrations.adapters.google_calendar.client.OAuth2Credentials",
        return_value=mocker.MagicMock(),
    )
    mocker.patch(
        "app.integrations.adapters.google_calendar.client.build",
        return_value=mocker.MagicMock(),
    )

    client = GoogleCalendarClient(credentials=empty_credentials)

    oauth_mock.assert_called_once_with(
        token=None,
        refresh_token=None,
        token_uri="https://oauth2.googleapis.com/token",  # noqa: S106
        client_id=None,
        client_secret=None,
        scopes=[],
    )
    assert client._service is not None


def test_create_client_with_custom_scopes_and_token_uri(mocker, mock_credentials):
    custom_credentials = {
        **mock_credentials,
        "scope": "https://www.googleapis.com/auth/calendar.readonly",
        "token_uri": "https://custom.oauth.com/token",
    }

    mock_oauth_creds = mocker.MagicMock()
    oauth_mock = mocker.patch(
        "app.integrations.adapters.google_calendar.client.OAuth2Credentials",
        return_value=mock_oauth_creds,
    )
    mocker.patch(
        "app.integrations.adapters.google_calendar.client.build",
        return_value=mocker.MagicMock(),
    )

    client = GoogleCalendarClient(credentials=custom_credentials)

    oauth_mock.assert_called_once_with(
        token="mock_access_token",  # noqa: S106
        refresh_token="mock_refresh_token",  # noqa: S106
        token_uri="https://custom.oauth.com/token",  # noqa: S106
        client_id="mock_client_id",
        client_secret="mock_client_secret",  # noqa: S106
        scopes=["https://www.googleapis.com/auth/calendar.readonly"],
    )
    assert client._service is not None
