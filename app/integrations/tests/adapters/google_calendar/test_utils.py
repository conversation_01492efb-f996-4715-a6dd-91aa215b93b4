import datetime

from app.integrations.adapters.google_calendar.utils import (
    FREQUENCY_MAP,
    convert_google_calendar_to_calendar,
    convert_google_event_to_calendar_event,
    parse_google_attendee,
    parse_google_datetime,
    parse_google_recurrence,
)


class TestParseGoogleDateTime:
    def test_parse_datetime_with_datetime(self):
        dt_data = {
            "dateTime": "2024-01-01T10:00:00Z",
            "timeZone": "UTC",
        }
        result = parse_google_datetime(dt_data)

        assert result is not None
        assert (
            result["date_time"]
            == datetime.datetime(2024, 1, 1, 10, 0, 0, tzinfo=datetime.UTC).isoformat()
        )
        assert result["day"] is None
        assert result["timezone"] == "UTC"

    def test_parse_datetime_with_date(self):
        dt_data = {
            "date": "2024-01-01",
            "timeZone": "UTC",
        }
        result = parse_google_datetime(dt_data)

        assert result is not None
        assert result["day"] == datetime.date(2024, 1, 1).isoformat()
        assert result["date_time"] is None
        assert result["timezone"] == "UTC"

    def test_parse_datetime_with_timezone_offset(self):
        dt_data = {
            "dateTime": "2024-01-01T10:00:00-05:00",
            "timeZone": "America/New_York",
        }
        result = parse_google_datetime(dt_data)

        assert result is not None
        # Parse the ISO string back to check the hour
        parsed_dt = datetime.datetime.fromisoformat(result["date_time"])
        assert parsed_dt.hour == 10
        assert result["timezone"] == "America/New_York"

    def test_parse_datetime_none_input(self):
        result = parse_google_datetime(None)
        assert result is None

    def test_parse_datetime_empty_dict(self):
        result = parse_google_datetime({})
        assert result is None

    def test_parse_datetime_no_datetime_or_date(self):
        dt_data = {"timeZone": "UTC"}
        result = parse_google_datetime(dt_data)
        assert result is None


class TestParseGoogleAttendee:
    def test_parse_attendee_full_data(self):
        attendee_data = {
            "email": "<EMAIL>",
            "displayName": "Test User",
            "responseStatus": "accepted",
            "organizer": True,
            "optional": False,
        }
        result = parse_google_attendee(attendee_data)

        assert result["email"] == "<EMAIL>"
        assert result["name"] == "Test User"
        assert result["response_status"] == "accepted"
        assert result["is_organizer"] is True
        assert result["is_optional"] is False

    def test_parse_attendee_minimal_data(self):
        attendee_data = {"email": "<EMAIL>"}
        result = parse_google_attendee(attendee_data)

        assert result["email"] == "<EMAIL>"
        assert result["name"] is None
        assert result["response_status"] is None
        assert result["is_organizer"] is False
        assert result["is_optional"] is False

    def test_parse_attendee_empty_email(self):
        attendee_data = {}
        result = parse_google_attendee(attendee_data)

        assert result["email"] == ""
        assert result["name"] is None
        assert result["response_status"] is None
        assert result["is_organizer"] is False
        assert result["is_optional"] is False


class TestParseGoogleRecurrence:
    def test_parse_recurrence_daily(self):
        recurrence_data = ["RRULE:FREQ=DAILY;INTERVAL=1;COUNT=10"]
        result = parse_google_recurrence(recurrence_data)

        assert result is not None
        assert result["frequency"] == "DAILY"
        assert result["interval"] == 1
        assert result["count"] == 10
        assert result["until"] is None

    def test_parse_recurrence_weekly_with_until(self):
        recurrence_data = ["RRULE:FREQ=WEEKLY;INTERVAL=2;UNTIL=20241231T235959Z"]
        result = parse_google_recurrence(recurrence_data)

        assert result is not None
        assert result["frequency"] == "WEEKLY"
        assert result["interval"] == 2
        assert (
            result["until"] == datetime.datetime(2024, 12, 31, 23, 59, 59).isoformat()
        )

    def test_parse_recurrence_with_byday(self):
        recurrence_data = ["RRULE:FREQ=WEEKLY;BYDAY=MO,WE,FR"]
        result = parse_google_recurrence(recurrence_data)

        assert result is not None
        assert result["frequency"] == "WEEKLY"
        assert result["by_day"] == ["MO", "WE", "FR"]

    def test_parse_recurrence_none_input(self):
        result = parse_google_recurrence(None)
        assert result is None

    def test_parse_recurrence_empty_list(self):
        result = parse_google_recurrence([])
        assert result is None

    def test_parse_recurrence_invalid_format(self):
        recurrence_data = ["INVALID:FREQ=DAILY"]
        result = parse_google_recurrence(recurrence_data)
        assert result is None

    def test_parse_recurrence_unknown_frequency(self):
        recurrence_data = ["RRULE:FREQ=UNKNOWN"]
        result = parse_google_recurrence(recurrence_data)

        assert result is not None
        assert result["frequency"] == "DAILY"


class TestConvertGoogleEventToCalendarEvent:
    def test_convert_event_full_data(self):
        event_data = {
            "id": "event123",
            "summary": "Test Event",
            "description": "Test Description",
            "location": "Test Location",
            "start": {"dateTime": "2024-01-01T10:00:00Z"},
            "end": {"dateTime": "2024-01-01T11:00:00Z"},
            "status": "confirmed",
            "visibility": "public",
            "created": "2024-01-01T09:00:00Z",
            "updated": "2024-01-01T09:30:00Z",
            "htmlLink": "https://calendar.google.com/event?eid=123",
            "hangoutLink": "https://meet.google.com/abc-def-ghi",
            "organizer": {"email": "<EMAIL>", "displayName": "Organizer"},
            "attendees": [
                {
                    "email": "<EMAIL>",
                    "displayName": "Attendee",
                    "responseStatus": "accepted",
                }
            ],
            "recurrence": ["RRULE:FREQ=WEEKLY"],
        }

        result = convert_google_event_to_calendar_event(event_data)

        assert result["id"] == "event123"
        assert result["title"] == "Test Event"
        assert result["description"] == "Test Description"
        assert result["location"] == "Test Location"
        assert result["status"] == "confirmed"
        assert result["visibility"] == "public"
        assert result["html_link"] == "https://calendar.google.com/event?eid=123"
        assert result["meeting_url"] == "https://meet.google.com/abc-def-ghi"
        assert len(result["attendees"]) == 1
        assert result["attendees"][0]["email"] == "<EMAIL>"
        assert result["organizer"]["email"] == "<EMAIL>"
        assert result["recurrence"] is not None
        assert result["raw_data"] == event_data

    def test_convert_event_minimal_data(self):
        event_data = {
            "id": "event123",
            "start": {"dateTime": "2024-01-01T10:00:00Z"},
            "end": {"dateTime": "2024-01-01T11:00:00Z"},
        }

        result = convert_google_event_to_calendar_event(event_data)

        assert result["id"] == "event123"
        assert result["title"] == ""
        assert result["description"] is None
        assert result["location"] is None
        assert result["status"] == "confirmed"
        assert result["raw_data"] == event_data
        assert result["start"] is not None
        assert result["end"] is not None


class TestConvertGoogleCalendarToCalendar:
    def test_convert_calendar_full_data(self):
        calendar_data = {
            "id": "primary",
            "summary": "Test Calendar",
            "description": "Test Description",
            "timeZone": "UTC",
            "primary": True,
            "accessRole": "owner",
            "colorId": "1",
            "backgroundColor": "#ff0000",
            "foregroundColor": "#ffffff",
        }

        result = convert_google_calendar_to_calendar(calendar_data)

        assert result["id"] == "primary"
        assert result["name"] == "Test Calendar"
        assert result["description"] == "Test Description"
        assert result["timezone"] == "UTC"
        assert result["is_primary"] is True
        assert result["access_role"] == "owner"
        assert result["color_id"] == "1"
        assert result["background_color"] == "#ff0000"
        assert result["foreground_color"] == "#ffffff"
        assert result["raw_data"] == calendar_data

    def test_convert_calendar_minimal_data(self):
        calendar_data = {
            "id": "calendar123",
        }

        result = convert_google_calendar_to_calendar(calendar_data)

        assert result["id"] == "calendar123"
        assert result["name"] == ""
        assert result["description"] is None
        assert result["is_primary"] is False
        assert result["access_role"] == "reader"
        assert result["raw_data"] == calendar_data


class TestFrequencyMap:
    def test_frequency_map_values(self):
        assert FREQUENCY_MAP["daily"] == "DAILY"
        assert FREQUENCY_MAP["weekly"] == "WEEKLY"
        assert FREQUENCY_MAP["monthly"] == "MONTHLY"
        assert FREQUENCY_MAP["yearly"] == "YEARLY"
