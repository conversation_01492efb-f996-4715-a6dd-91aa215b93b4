import pytest

from app.integrations.adapters.salesforce.access_resolver import (
    SalesforceAccountAccessResolver,
)


@pytest.fixture
def mock_salesforce_client(mocker):
    client_mock = mocker.Mock()
    client_mock.get_available_objects = mocker.AsyncMock(
        return_value={
            "AccountTeamMember",
            "Territory2",
            "UserTerritory2Association",
            "ObjectTerritory2Association",
        }
    )
    client_mock.list_objects_by_owner = mocker.AsyncMock()
    client_mock.query = mocker.AsyncMock()
    client_mock.list_objects_by_ids = mocker.AsyncMock()
    return client_mock


@pytest.fixture
def mock_resolver(mock_salesforce_client):
    resolver = SalesforceAccountAccessResolver(client=mock_salesforce_client)
    return resolver


@pytest.mark.anyio
async def test_check_available_objects(mock_resolver):
    await mock_resolver._check_available_objects()
    assert mock_resolver._has_team_member is True
    assert mock_resolver._has_territory is True


@pytest.mark.anyio
async def test_check_available_objects_with_missing_objects(mocker):
    client_mock = mocker.Mock()
    client_mock.get_available_objects = mocker.AsyncMock(return_value=set())
    resolver = SalesforceAccountAccessResolver(client=client_mock)
    await resolver._check_available_objects()
    assert resolver._has_team_member is False
    assert resolver._has_territory is False


@pytest.mark.anyio
async def test_get_owned_accounts(mock_resolver):
    mock_resolver.salesforce_client.list_objects_by_owner.return_value = [
        {"Id": "account1", "Name": "Account One"},
        {"Id": "account2", "Name": "Account Two"},
    ]
    user_id = "user123"
    owned_accounts = await mock_resolver._get_owned_accounts(user_id)
    mock_resolver.salesforce_client.list_objects_by_owner.assert_called_once_with(
        "Account", owner_id=user_id, fields=["Id", "Name"]
    )
    assert len(owned_accounts) == 2
    assert all(account.get("access_type") == "owner" for account in owned_accounts)
    assert {account["Id"] for account in owned_accounts} == {"account1", "account2"}


@pytest.mark.anyio
async def test_get_team_accounts(mock_resolver):
    mock_resolver._has_team_member = True
    mock_resolver.salesforce_client.query.return_value = {
        "records": [
            {"AccountId": "team_account1", "TeamMemberRole": "Manager"},
            {"AccountId": "team_account2", "TeamMemberRole": "Member"},
        ]
    }
    mock_resolver.salesforce_client.list_objects_by_ids.return_value = [
        {"Id": "team_account1", "Name": "Team Account One"},
        {"Id": "team_account2", "Name": "Team Account Two"},
    ]
    user_id = "user123"
    team_accounts = await mock_resolver._get_team_accounts(user_id)
    assert team_accounts[0]["role"] == "Manager"
    assert team_accounts[1]["role"] == "Member"
    assert all(account.get("access_type") == "team" for account in team_accounts)


@pytest.mark.anyio
async def test_get_territory_accounts(mock_resolver):
    mock_resolver._has_territory = True
    mock_resolver.salesforce_client.query.side_effect = [
        {"records": [{"Territory2Id": "territory1"}, {"Territory2Id": "territory2"}]},
        {
            "records": [
                {"Id": "territory_account1", "Name": "Territory Account One"},
                {"Id": "territory_account2", "Name": "Territory Account Two"},
            ]
        },
    ]
    user_id = "user123"
    territory_accounts = await mock_resolver._get_territory_accounts(user_id)
    assert len(territory_accounts) == 2
    assert all(
        account.get("access_type") == "territory" for account in territory_accounts
    )


def test_deduplicate_accounts(mock_resolver):
    input_accounts = [
        {"Id": "account1", "access_type": "team", "Name": "Team Account"},
        {"Id": "account1", "access_type": "owner", "Name": "Owner Account"},
        {"Id": "account2", "access_type": "territory", "Name": "Territory Account"},
        {"Id": "account3", "access_type": "owner", "Name": "Another Owner Account"},
    ]
    deduplicated = mock_resolver._deduplicate_accounts(input_accounts)
    assert len(deduplicated) == 3
    account1 = next(acc for acc in deduplicated if acc["Id"] == "account1")
    assert account1["access_type"] == "owner"
    assert account1["Name"] == "Owner Account"


@pytest.mark.anyio
async def test_get_user_account_access(mock_resolver):
    mock_resolver._has_team_member = True
    mock_resolver._has_territory = True

    mock_resolver.salesforce_client.list_objects_by_owner.return_value = [
        {"Id": "owned_account", "Name": "Owned Account"}
    ]
    mock_resolver.salesforce_client.query.side_effect = [
        {"records": [{"AccountId": "team_account", "TeamMemberRole": "Member"}]},
        {"records": [{"Territory2Id": "territory1"}]},
        {"records": [{"Id": "territory_account", "Name": "Territory Account"}]},
    ]
    mock_resolver.salesforce_client.list_objects_by_ids.return_value = [
        {"Id": "team_account", "Name": "Team Account"}
    ]
    user_id = "user123"
    account_access = await mock_resolver.get_user_account_access(user_id)
    assert len(account_access) == 3
    access_types = {acc.access_type for acc in account_access}
    assert access_types == {"owner", "team", "territory"}


@pytest.mark.anyio
async def test_error_handling_in_account_retrieval(mock_resolver):
    mock_resolver.salesforce_client.list_objects_by_owner.side_effect = Exception(
        "Retrieval error"
    )
    user_id = "user123"
    with pytest.raises(Exception, match="Retrieval error"):
        await mock_resolver.get_user_account_access(user_id)
