import datetime

from app.integrations.adapters.slack.utils import (
    convert_slack_message_to_message_data,
)


def test_convert_slack_message_to_message_data():
    slack_message = {
        "ts": "1617293942.001200",
        "text": "Hello world",
        "user": "U12345",
        "channel": "C67890",
        "edited": {"ts": "1617293950.001300"},
    }

    message_data = convert_slack_message_to_message_data(slack_message, "C12345")

    assert message_data.message_id == "C67890:1617293942.001200"
    assert message_data.content == "Hello world"
    assert message_data.author == "U12345"
    assert message_data.thread_id is None
    assert message_data.channel_id == "C67890"  # Should use the one from the message
    assert message_data.sent_at == datetime.datetime.fromtimestamp(
        1617293942.0012, tz=datetime.UTC
    )
    assert message_data.last_edit_at == datetime.datetime.fromtimestamp(
        1617293950.0013, tz=datetime.UTC
    )
    assert message_data.tombstone is False


def test_convert_slack_thread_message_to_message_data():
    slack_message = {
        "ts": "1617293942.001200",
        "text": "Hello world",
        "user": "U12345",
        "thread_ts": "1617293942.001200",
        "channel": "C67890",
        "edited": {"ts": "1617293950.001300"},
    }

    message_data = convert_slack_message_to_message_data(slack_message, "C12345")

    assert message_data.message_id == "C67890:1617293942.001200"
    assert message_data.content == "Hello world"
    assert message_data.author == "U12345"
    assert message_data.thread_id == "C67890:1617293942.001200"
    assert message_data.channel_id == "C67890"  # Should use the one from the message
    assert message_data.sent_at == datetime.datetime.fromtimestamp(
        1617293942.0012, tz=datetime.UTC
    )
    assert message_data.last_edit_at == datetime.datetime.fromtimestamp(
        1617293950.0013, tz=datetime.UTC
    )
    assert message_data.tombstone is False


def test_convert_slack_message_to_message_data_tombstone():
    slack_message = {
        "ts": "1617293942.001200",
        "text": "This message was deleted",
        "user": "U12345",
        "subtype": "tombstone",
    }

    message_data = convert_slack_message_to_message_data(slack_message, "C12345")

    assert message_data.tombstone is True
    assert message_data.channel_id == "C12345"  # Should use the provided channel ID


def test_convert_slack_message_to_message_data_with_parent_id():
    slack_message = {
        "ts": "1617293943.001200",
        "text": "Thread reply message",
        "user": "U12345",
        "thread_ts": "1617293942.001200",  # Different from message's ts
        "channel": "C67890",
    }

    message_data = convert_slack_message_to_message_data(slack_message, "C12345")

    assert message_data.parent_id == "C67890:1617293942.001200"
    assert message_data.thread_id == "C67890:1617293942.001200"


def test_convert_slack_message_to_message_data_thread_root_no_parent():
    slack_message = {
        "ts": "1617293942.001200",
        "text": "Thread root message",
        "user": "U12345",
        "thread_ts": "1617293942.001200",  # Same as ts
        "channel": "C67890",
    }

    message_data = convert_slack_message_to_message_data(slack_message, "C12345")

    assert message_data.parent_id is None
    assert message_data.thread_id == "C67890:1617293942.001200"


def test_convert_slack_message_to_message_data_standalone_message():
    slack_message = {
        "ts": "1617293942.001200",
        "text": "Standalone message",
        "user": "U12345",
        "channel": "C67890",
    }

    message_data = convert_slack_message_to_message_data(slack_message, "C12345")

    assert message_data.parent_id is None
    assert message_data.thread_id is None
