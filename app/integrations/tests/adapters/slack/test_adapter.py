from datetime import UTC, datetime

import pytest

from app.integrations.adapters.slack.adapter import <PERSON>lackAdapter
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.schemas import ChannelDataSlice, MessageData
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials(mocker):
    mock_creds = mocker.Mock(spec=ICredentials)
    mock_creds.secrets = {"slack_token": "xoxb-test-token"}
    return mock_creds


@pytest.fixture
def mock_slack_client(mocker):
    mock_client = mocker.MagicMock()

    async def mock_get_channel_history(*_args, **_kwargs):
        return [
            {
                "ts": "1609459200.000001",
                "text": "Hello world",
                "user": "U12345",
                "channel": "C12345",
            }
        ]

    async def mock_get_thread_replies(*_args, **_kwargs):
        return [
            {
                "ts": "1609459300.000001",
                "text": "Reply to hello",
                "user": "U67890",
                "channel": "C12345",
                "thread_ts": "1609459200.000001",
            }
        ]

    async def mock_get_channel_info(*_args, **_kwargs):
        return {"id": "C12345", "name": "general"}

    async def mock_join_channel(*_args, **_kwargs):
        return None

    mock_client.get_channel_history = mock_get_channel_history
    mock_client.get_thread_replies = mock_get_thread_replies
    mock_client.get_channel_info = mock_get_channel_info
    mock_client.join_channel = mock_join_channel

    mock_client.find_threaded_messages.return_value = [
        {
            "ts": "1609459200.000001",
            "thread_ts": "1609459200.000001",
        }
    ]

    return mock_client


@pytest.fixture
def slack_adapter(mock_credentials, mocker, mock_slack_client):
    mocker.patch.object(SlackAdapter, "_create_client", return_value=mock_slack_client)
    return SlackAdapter(credentials=mock_credentials)


def test_init(mock_credentials, mocker, mock_slack_client):
    mocker.patch.object(SlackAdapter, "_create_client", return_value=mock_slack_client)

    adapter = SlackAdapter(credentials=mock_credentials)

    assert adapter.credentials == mock_credentials
    assert adapter._client is mock_slack_client


def test_init_missing_slack_token(mocker):
    mock_creds = mocker.Mock(spec=ICredentials)
    mock_creds.secrets = {}

    with pytest.raises(ValueError, match="Slack token not found in credentials"):
        SlackAdapter(credentials=mock_creds)


def test_source(slack_adapter):
    assert slack_adapter.source == IntegrationSource.SLACK


@pytest.mark.anyio
async def test_get_channel_data(slack_adapter):
    channel_id = "C12345"
    start_time = datetime(2021, 1, 1, tzinfo=UTC)
    end_time = datetime(2021, 1, 2, tzinfo=UTC)
    batch_size = 100

    result = await slack_adapter.get_channel_data(
        channel_id=channel_id,
        start_time=start_time,
        end_time=end_time,
        batch_size=batch_size,
    )

    assert isinstance(result, ChannelDataSlice)
    assert result.channel_id == channel_id
    assert result.from_time == start_time
    assert result.to_time == end_time
    assert len(result.messages) == 2

    main_message = result.messages[0]
    assert isinstance(main_message, MessageData)
    assert main_message.message_id == "C12345:1609459200.000001"
    assert main_message.content == "Hello world"
    assert main_message.author == "U12345"

    reply_message = result.messages[1]
    assert isinstance(reply_message, MessageData)
    assert reply_message.message_id == "C12345:1609459300.000001"
    assert reply_message.content == "Reply to hello"
    assert reply_message.author == "U67890"
    assert reply_message.thread_id == "C12345:1609459200.000001"
    assert reply_message.parent_id == "C12345:1609459200.000001"


@pytest.mark.anyio
async def test_join_channel(slack_adapter):
    channel_id = "C12345"

    result = await slack_adapter.join_channel(channel_id=channel_id)

    assert result is True


@pytest.mark.anyio
async def test_join_channel_already_in_channel(slack_adapter, mock_slack_client):
    channel_id = "C12345"

    async def mock_join_with_error(*_args, **_kwargs):
        raise Exception("already_in_channel")

    mock_slack_client.join_channel = mock_join_with_error

    result = await slack_adapter.join_channel(channel_id=channel_id)

    assert result is True


@pytest.mark.anyio
async def test_join_channel_error(slack_adapter, mock_slack_client):
    channel_id = "C12345"

    async def mock_join_with_error(*_args, **_kwargs):
        raise Exception("Some other error")

    mock_slack_client.join_channel = mock_join_with_error

    result = await slack_adapter.join_channel(channel_id=channel_id)

    assert result is False


@pytest.mark.anyio
async def test_get_channel_info(
    slack_adapter,
):
    channel_id = "C12345"

    result = await slack_adapter.get_channel_info(channel_id=channel_id)

    assert result["id"] == "C12345"
    assert result["name"] == "general"


@pytest.mark.anyio
async def test_get_channel_info_error(slack_adapter, mock_slack_client):
    channel_id = "C12345"

    async def mock_get_info_with_error(*_args, **_kwargs):
        raise Exception("Channel not found")

    mock_slack_client.get_channel_info = mock_get_info_with_error

    with pytest.raises(ValueError, match="Error getting channel info"):
        await slack_adapter.get_channel_info(channel_id=channel_id)


@pytest.mark.anyio
async def test_internal_fetch_channel_messages(slack_adapter):
    channel_id = "C12345"
    start_time = datetime(2021, 1, 1, tzinfo=UTC)
    end_time = datetime(2021, 1, 2, tzinfo=UTC)
    batch_size = 100

    result = await slack_adapter._fetch_channel_messages(
        channel_id=channel_id,
        start_time=start_time,
        end_time=end_time,
        batch_size=batch_size,
    )

    assert len(result) == 1
    assert result[0]["text"] == "Hello world"


@pytest.mark.anyio
async def test_internal_fetch_channel_messages_with_filtering(
    slack_adapter, mock_slack_client
):
    channel_id = "C12345"
    start_time = datetime(2021, 1, 1, tzinfo=UTC)
    end_time = datetime(2021, 1, 2, tzinfo=UTC)
    batch_size = 100

    async def mock_get_channel_history_with_filtered(*_args, **_kwargs):
        return [
            {
                "ts": "1609459200.000001",
                "text": "Hello world",
                "user": "U12345",
                "channel": "C12345",
            },
            {
                "ts": "1609459200.000002",
                "subtype": "channel_join",
                "text": "User joined channel",
                "user": "U67890",
                "channel": "C12345",
            },
            {
                "ts": "1609459200.000003",
                "subtype": "thread_broadcast",
                "text": "Thread broadcast",
                "user": "U11111",
                "channel": "C12345",
            },
        ]

    mock_slack_client.get_channel_history = mock_get_channel_history_with_filtered

    result = await slack_adapter._fetch_channel_messages(
        channel_id=channel_id,
        start_time=start_time,
        end_time=end_time,
        batch_size=batch_size,
    )

    # Should filter out channel_join and thread_broadcast
    assert len(result) == 1
    assert result[0]["text"] == "Hello world"
    assert result[0]["ts"] == "1609459200.000001"


@pytest.mark.anyio
async def test_internal_enrich_with_threaded_replies(slack_adapter, mock_slack_client):
    channel_id = "C12345"
    messages = [
        {
            "ts": "1609459200.000001",
            "thread_ts": "1609459200.000001",
            "text": "Hello world",
        }
    ]
    batch_size = 100

    result = await slack_adapter._enrich_with_threaded_replies(
        channel_id=channel_id,
        messages=messages,
        batch_size=batch_size,
    )

    mock_slack_client.find_threaded_messages.assert_called_once_with(messages)

    assert len(result) == 2
    assert result[0]["ts"] == "1609459200.000001"
    assert result[1]["ts"] == "1609459300.000001"
    assert result[1]["text"] == "Reply to hello"


def test_internal_find_threaded_messages(slack_adapter, mock_slack_client):
    messages = [
        {"ts": "1609459200.000001", "thread_ts": "1609459200.000001"},
        {"ts": "1609459300.000001"},
    ]

    result = slack_adapter._find_threaded_messages(messages=messages)

    mock_slack_client.find_threaded_messages.assert_called_once_with(messages)

    assert len(result) == 1
    assert result[0]["ts"] == "1609459200.000001"
