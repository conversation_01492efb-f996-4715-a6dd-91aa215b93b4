import datetime

from app.integrations.schemas import MessageData


def test_valid_message_data():
    data = {
        "message_id": "msg1",
        "channel_id": "general",
        "content": "Hello World",
        "sent_at": datetime.datetime.now(datetime.UTC),
        "last_edit_at": None,
        "tombstone": False,
        "author": "user1",
        "thread_id": "thread1",
    }

    message = MessageData(**data)

    assert message.message_id == "msg1"
    assert message.channel_id == "general"
    assert message.tombstone is False
    assert isinstance(message.sent_at, datetime.datetime)
    assert message.sent_at.tzinfo is not None


def test_model_dump_and_validate():
    data = {
        "message_id": "msg3",
        "channel_id": "random",
        "content": "Test message",
        "sent_at": datetime.datetime.now(datetime.UTC),
        "last_edit_at": datetime.datetime.now(datetime.UTC),
        "tombstone": False,
        "author": "user3",
        "thread_id": None,
    }

    message = MessageData(**data)
    dumped = message.model_dump_json()
    message_reloaded = MessageData.model_validate_json(dumped)

    assert message_reloaded.message_id == message.message_id
    assert message_reloaded.channel_id == message.channel_id
