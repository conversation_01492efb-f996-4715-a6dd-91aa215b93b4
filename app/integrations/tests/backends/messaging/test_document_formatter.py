import datetime

from app.integrations.backends.messaging.document_formatter import (
    MessageDocumentFormatter,
)
from app.integrations.schemas import MessageData


def test_format_document_content_main_message_only():
    formatter = MessageDocumentFormatter()

    main_message = MessageData(
        message_id="msg1",
        channel_id="C12345",
        content="Hello, world!",
        sent_at=datetime.datetime.now(),
        author="user1",
    )

    formatted_content = formatter.format_document_content(main_message)

    assert formatted_content == "Hello, world!"


def test_format_document_content_with_context():
    formatter = MessageDocumentFormatter()

    main_message = MessageData(
        message_id="msg1",
        channel_id="C12345",
        content="Current message",
        sent_at=datetime.datetime.now(),
        author="user1",
    )

    context_messages = [
        MessageData(
            message_id="msg_context1",
            channel_id="C12345",
            content="Previous context 1",
            sent_at=datetime.datetime.now() - datetime.timedelta(minutes=1),
            author="user2",
        ),
        MessageData(
            message_id="msg_context2",
            channel_id="C12345",
            content="Previous context 2",
            sent_at=datetime.datetime.now() - datetime.timedelta(minutes=2),
            author="user3",
        ),
    ]

    formatted_content = formatter.format_document_content(
        main_message, context_messages=context_messages
    )

    expected_content = (
        "Current message\n\nPrevious messages:\nPrevious context 1\nPrevious context 2"
    )

    assert formatted_content == expected_content


def test_format_document_content_with_replies():
    formatter = MessageDocumentFormatter()

    main_message = MessageData(
        message_id="msg1",
        channel_id="C12345",
        content="Main discussion",
        sent_at=datetime.datetime.now(),
        author="user1",
        thread_id="thread1",
    )

    replies = [
        MessageData(
            message_id="reply1",
            channel_id="C12345",
            content="First reply",
            sent_at=datetime.datetime.now() + datetime.timedelta(minutes=1),
            author="user2",
            thread_id="thread1",
        ),
        MessageData(
            message_id="reply2",
            channel_id="C12345",
            content="Second reply",
            sent_at=datetime.datetime.now() + datetime.timedelta(minutes=2),
            author="user3",
            thread_id="thread1",
        ),
    ]

    formatted_content = formatter.format_document_content(main_message, replies=replies)

    expected_content = "Main discussion\n\nReplies:\nFirst reply\nSecond reply"

    assert formatted_content == expected_content


def test_format_document_content_with_context_and_replies():
    formatter = MessageDocumentFormatter()

    main_message = MessageData(
        message_id="msg1",
        channel_id="C12345",
        content="Current message",
        sent_at=datetime.datetime.now(),
        author="user1",
        thread_id="thread1",
    )

    context_messages = [
        MessageData(
            message_id="msg_context1",
            channel_id="C12345",
            content="Previous context 1",
            sent_at=datetime.datetime.now() - datetime.timedelta(minutes=1),
            author="user2",
        )
    ]

    replies = [
        MessageData(
            message_id="reply1",
            channel_id="C12345",
            content="First reply",
            sent_at=datetime.datetime.now() + datetime.timedelta(minutes=1),
            author="user3",
            thread_id="thread1",
        )
    ]

    formatted_content = formatter.format_document_content(
        main_message, context_messages=context_messages, replies=replies
    )

    expected_content = (
        "Current message\n\n"
        "Previous messages:\n"
        "Previous context 1\n\n"
        "Replies:\n"
        "First reply"
    )

    assert formatted_content == expected_content


def test_format_document_content_empty_lists():
    formatter = MessageDocumentFormatter()

    main_message = MessageData(
        message_id="msg1",
        channel_id="C12345",
        content="Standalone message",
        sent_at=datetime.datetime.now(),
        author="user1",
    )

    formatted_content = formatter.format_document_content(
        main_message, context_messages=[], replies=[]
    )

    assert formatted_content == "Standalone message"
