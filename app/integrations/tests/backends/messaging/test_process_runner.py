import uuid

import pytest

from app.common.task_runner.runner import <PERSON>Runner
from app.integrations.backends.messaging.channel_processor import (
    MessagingChannelProcessor,
)
from app.integrations.backends.messaging.process_runner import MessagingProcessRunner
from app.integrations.backends.messaging.process_stage import MessagingProcessStage
from app.integrations.context import IntegrationContext
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_context(mocker):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = uuid.uuid4()

    def create_mock_session():
        mock_session = mocker.Mock()
        mock_session.__aenter__ = mocker.AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = mocker.AsyncMock(return_value=None)
        return mock_session

    mock_context.db_session_factory = create_mock_session
    return mock_context


@pytest.fixture
def process_runner(mock_context):
    return MessagingProcessRunner(
        context=mock_context,
        source=IntegrationSource.SLACK,
    )


def test_init(mock_context):
    runner = MessagingProcessRunner(
        context=mock_context,
        source=IntegrationSource.SLACK,
    )

    assert runner.context == mock_context
    assert runner.source == IntegrationSource.SLACK
    assert runner.tenant_id == mock_context.tenant_id
    assert runner.db_session_factory == mock_context.db_session_factory


@pytest.mark.anyio
async def test_run_normal_mode(mocker, process_runner):
    mock_pipeline = mocker.Mock(spec=TaskRunner)
    mock_pipeline.run = mocker.AsyncMock(
        return_value={"status": "success", "details": "test results"}
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.TaskRunner",
        return_value=mock_pipeline,
    )

    mock_message_store_class = mocker.patch(
        "app.integrations.backends.messaging.process_runner.PostgresMessageStore"
    )
    mock_cursor_store_class = mocker.patch(
        "app.integrations.backends.messaging.process_runner.PostgresCursorStore"
    )
    mock_document_store_class = mocker.patch(
        "app.integrations.backends.messaging.process_runner.PostgresDocumentStore"
    )

    mocker.patch(
        "app.integrations.backends.messaging.process_runner.ChannelChangelogReader"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.MessageWithContextToDocumentIndexer"
    )

    mock_processor = mocker.Mock(spec=MessagingChannelProcessor)
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.MessagingChannelProcessor",
        return_value=mock_processor,
    )

    mock_stage = mocker.Mock(spec=MessagingProcessStage)
    stage_init_mock = mocker.patch(
        "app.integrations.backends.messaging.process_runner.MessagingProcessStage",
        return_value=mock_stage,
    )

    channel_ids = ["channel1", "channel2"]
    result = await process_runner.run(
        channel_ids=channel_ids,
        interval_seconds=300,
        batch_size=100,
        daemon_mode=False,
    )

    mock_message_store_class.assert_called_once()
    message_store_args = mock_message_store_class.call_args.kwargs
    assert message_store_args["tenant_id"] == process_runner.tenant_id
    assert message_store_args["source"] == process_runner.source

    mock_cursor_store_class.assert_called_once()
    cursor_store_args = mock_cursor_store_class.call_args.kwargs
    assert cursor_store_args["tenant_id"] == process_runner.tenant_id

    mock_document_store_class.assert_called_once()
    document_store_args = mock_document_store_class.call_args.kwargs
    assert document_store_args["tenant_id"] == process_runner.tenant_id
    assert document_store_args["source"] == process_runner.source

    stage_init_mock.assert_called_once()
    stage_args = stage_init_mock.call_args.kwargs
    assert stage_args["tenant_id"] == process_runner.tenant_id
    assert stage_args["source"] == IntegrationSource.SLACK
    assert stage_args["processor"] == mock_processor
    assert stage_args["channel_ids"] == channel_ids
    assert stage_args["interval_seconds"] == 300

    assert mock_pipeline.add_task.call_count == 1
    assert mock_pipeline.run.call_count == 1
    assert result == {"status": "success", "details": "test results"}


@pytest.mark.anyio
async def test_run_daemon_mode(mocker, process_runner):
    mock_pipeline = mocker.Mock(spec=TaskRunner)
    mock_pipeline.start_daemon = mocker.AsyncMock()
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.TaskRunner",
        return_value=mock_pipeline,
    )

    mocker.patch(
        "app.integrations.backends.messaging.process_runner.PostgresMessageStore"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.PostgresCursorStore"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.PostgresDocumentStore"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.ChannelChangelogReader"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.MessageWithContextToDocumentIndexer"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.MessagingChannelProcessor"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.MessagingProcessStage"
    )

    result = await process_runner.run(
        channel_ids=["channel1"],
        daemon_mode=True,
    )

    assert result == {"status": "daemon_stopped"}
    assert mock_pipeline.start_daemon.call_count == 1


@pytest.mark.anyio
async def test_run_custom_parameters(mocker, process_runner):
    mock_pipeline = mocker.Mock(spec=TaskRunner)
    mock_pipeline.run = mocker.AsyncMock(return_value={})
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.TaskRunner",
        return_value=mock_pipeline,
    )

    mocker.patch(
        "app.integrations.backends.messaging.process_runner.PostgresMessageStore"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.PostgresCursorStore"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.PostgresDocumentStore"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.ChannelChangelogReader"
    )
    mocker.patch(
        "app.integrations.backends.messaging.process_runner.MessageWithContextToDocumentIndexer"
    )

    processor_init_mock = mocker.patch(
        "app.integrations.backends.messaging.process_runner.MessagingChannelProcessor"
    )
    stage_init_mock = mocker.patch(
        "app.integrations.backends.messaging.process_runner.MessagingProcessStage"
    )

    custom_interval = 600
    custom_batch_size = 200

    await process_runner.run(
        channel_ids=["channel1", "channel2"],
        interval_seconds=custom_interval,
        batch_size=custom_batch_size,
    )

    processor_init_mock.assert_called_once()
    processor_args = processor_init_mock.call_args.kwargs
    assert processor_args["batch_size"] == custom_batch_size

    stage_init_mock.assert_called_once()
    stage_args = stage_init_mock.call_args.kwargs
    assert stage_args["interval_seconds"] == custom_interval
