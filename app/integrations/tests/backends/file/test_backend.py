import uuid

import pytest

from app.integrations.backends.file.backend import FileBackend
from app.integrations.backends.file.process_runner import FileProcessRunner
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import IntegrationContext
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_context(mocker):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = uuid.uuid4()

    mock_session = mocker.Mock()
    mock_session.__aenter__ = mocker.AsyncMock(return_value=mock_session)
    mock_session.__aexit__ = mocker.AsyncMock(return_value=None)

    mock_context.db_session_factory = mocker.Mock(return_value=mock_session)
    mock_context.credentials_resolver = mocker.Mock(spec=ICredentialsResolver)
    return mock_context


@pytest.fixture
def mock_adapter_class(mocker):
    mock_class = mocker.Mock()
    return mock_class


@pytest.fixture
def file_backend(mock_context, mock_adapter_class):
    return FileBackend(
        context=mock_context,
        adapter_class=mock_adapter_class,
        source=IntegrationSource.GCS,
    )


def test_file_backend_initialization(mock_context, mock_adapter_class):
    backend = FileBackend(
        context=mock_context,
        adapter_class=mock_adapter_class,
        source=IntegrationSource.GCS,
    )

    assert backend._context == mock_context
    assert backend._adapter_class == mock_adapter_class
    assert backend.source == IntegrationSource.GCS
    assert backend.tenant_id == mock_context.tenant_id

    assert isinstance(backend.process_runner, FileProcessRunner)


@pytest.mark.anyio
async def test_start_processing(file_backend, mocker):
    mock_process_runner = mocker.Mock(spec=FileProcessRunner)
    mock_process_runner.run = mocker.AsyncMock(return_value={"status": "success"})
    file_backend.process_runner = mock_process_runner

    bucket_names = ["test-bucket-1", "test-bucket-2"]
    extensions = ["txt"]

    result = await file_backend.start_processing(
        bucket_names=bucket_names, extensions=extensions
    )

    mock_process_runner.run.assert_called_once_with(
        bucket_names=bucket_names,
        extensions=extensions,
    )

    assert result == {"status": "success"}


@pytest.mark.anyio
async def test_search_files(file_backend, mocker):
    query = "test query"
    expected_result = [("doc1", 0.9), ("doc2", 0.8)]
    file_backend.search_files = mocker.AsyncMock(return_value=expected_result)

    result = await file_backend.search_files(query, limit=5)

    file_backend.search_files.assert_called_once_with(query, limit=5)
    assert result == expected_result
