import pytest

from app.integrations.processors.embedders.openai_embedder import (
    OpenAIEmbedder,
    OpenAIEmbeddingModel,
)


@pytest.fixture
def mock_openai_client(mocker):
    """Mock OpenAI client for testing."""
    mock_client = mocker.Mock()
    mock_client.embeddings.create = mocker.AsyncMock()
    mocker.patch(
        "app.integrations.processors.embedders.openai_embedder.AsyncOpenAI",
        return_value=mock_client,
    )
    return mock_client


@pytest.fixture
def openai_embedder(mocker, mock_openai_client):
    mock_openai_client.embeddings.create.return_value = mocker.Mock(
        data=[mocker.Mock(embedding=[0.1, 0.2, 0.3])]
    )
    return OpenAIEmbedder(api_key="test-api-key")


def test_openai_embedder_initialization():
    embedder = OpenAIEmbedder(api_key="test-api-key")

    assert embedder.model == OpenAIEmbeddingModel.TEXT_EMBEDDING_3_SMALL


def test_openai_embedder_initialization_with_custom_model():
    embedder = OpenAIEmbedder(
        api_key="test-api-key", model=OpenAIEmbeddingModel.TEXT_EMBEDDING_3_LARGE
    )

    assert embedder.model == OpenAIEmbeddingModel.TEXT_EMBEDDING_3_LARGE


@pytest.mark.anyio
async def test_embed_text_success(openai_embedder, mock_openai_client):
    result = await openai_embedder.embed_text("Hello, world!")

    assert result == [0.1, 0.2, 0.3]
    mock_openai_client.embeddings.create.assert_called_once_with(
        model=OpenAIEmbeddingModel.TEXT_EMBEDDING_3_SMALL,
        input="Hello, world!",
        encoding_format="float",
    )


@pytest.mark.anyio
async def test_embed_text_with_whitespace(openai_embedder, mock_openai_client):
    result = await openai_embedder.embed_text("  Hello, world!  ")

    assert result == [0.1, 0.2, 0.3]
    mock_openai_client.embeddings.create.assert_called_once_with(
        model=OpenAIEmbeddingModel.TEXT_EMBEDDING_3_SMALL,
        input="Hello, world!",
        encoding_format="float",
    )


@pytest.mark.anyio
async def test_embed_text_empty_string(openai_embedder):
    with pytest.raises(ValueError, match="Text cannot be empty or None"):
        await openai_embedder.embed_text("")


@pytest.mark.anyio
async def test_embed_text_whitespace_only(openai_embedder):
    with pytest.raises(ValueError, match="Text cannot be empty or None"):
        await openai_embedder.embed_text("   ")


@pytest.mark.anyio
async def test_embed_text_none(openai_embedder):
    with pytest.raises(ValueError, match="Text cannot be empty or None"):
        await openai_embedder.embed_text(None)


@pytest.mark.anyio
async def test_embed_text_unexpected_error(openai_embedder, mock_openai_client):
    mock_openai_client.embeddings.create.side_effect = Exception("Network error")

    with pytest.raises(Exception, match="Failed to generate embedding"):
        await openai_embedder.embed_text("Hello, world!")
