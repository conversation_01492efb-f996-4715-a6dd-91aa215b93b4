import base64
import json

import pytest

from app.integrations.processors.parsers.mistral_ocr import (
    ImageData,
    MistralOCR,
    MistralOCRResponse,
    PageData,
)


@pytest.fixture
def mistral_ocr():
    return MistralOCR(api_key="test_api_key")


@pytest.fixture
def mock_mistral_client(mocker):
    mock_client = mocker.MagicMock()
    mock_client.ocr = mocker.MagicMock()
    mock_client.ocr.process_async = mocker.AsyncMock()
    return mock_client


@pytest.fixture
def sample_pdf_data():
    return b"fake_pdf_content"


@pytest.fixture
def sample_image_data():
    return b"fake_image_content"


@pytest.fixture
def mock_ocr_response():
    return {
        "pages": [
            {
                "markdown": "# Page 1\n\nThis is page 1 content with ![img_1](img_1).",
                "images": [
                    {
                        "id": "img_1",
                        "image_annotation": '{"description": "A chart showing sales data"}',
                    }
                ],
            },
            {"markdown": "# Page 2\n\nThis is page 2 content.", "images": []},
        ]
    }


@pytest.mark.anyio
async def test_parse_pdf_success(
    mistral_ocr, mock_mistral_client, sample_pdf_data, mock_ocr_response, mocker
):
    mocker.patch.object(mistral_ocr, "client", mock_mistral_client)

    mock_response = mocker.MagicMock()
    mock_response.model_dump_json.return_value = json.dumps(mock_ocr_response)
    mock_mistral_client.ocr.process_async.return_value = mock_response

    result = await mistral_ocr.parse(sample_pdf_data, "application/pdf")

    mock_mistral_client.ocr.process_async.assert_called_once()
    call_args = mock_mistral_client.ocr.process_async.call_args

    assert call_args[1]["model"] == "mistral-ocr-latest"
    assert call_args[1]["include_image_base64"] is False

    document = call_args[1]["document"]
    assert document.type == "document_url"
    expected_url = f"data:application/pdf;base64,{base64.b64encode(sample_pdf_data).decode('utf-8')}"
    assert document.document_url == expected_url

    assert "Page 1" in result
    assert "Page 2" in result
    assert "A chart showing sales data" in result


@pytest.mark.anyio
async def test_parse_image_success(
    mistral_ocr, mock_mistral_client, sample_image_data, mock_ocr_response, mocker
):
    mocker.patch.object(mistral_ocr, "client", mock_mistral_client)

    mock_response = mocker.MagicMock()
    mock_response.model_dump_json.return_value = json.dumps(mock_ocr_response)
    mock_mistral_client.ocr.process_async.return_value = mock_response

    result = await mistral_ocr.parse(sample_image_data, "image/png")

    mock_mistral_client.ocr.process_async.assert_called_once()
    call_args = mock_mistral_client.ocr.process_async.call_args

    document = call_args[1]["document"]
    assert document.type == "image_url"
    expected_url = (
        f"data:image/png;base64,{base64.b64encode(sample_image_data).decode('utf-8')}"
    )
    assert document.image_url == expected_url

    assert "Page 1" in result
    assert "A chart showing sales data" in result


def test_extract_markdown_with_images_success(mistral_ocr):
    image_data = ImageData(
        id="img_1", image_annotation='{"description": "A beautiful sunset"}'
    )

    page1 = PageData(
        markdown="# Page 1\n\nContent with ![img_1](img_1) reference.",
        images=[image_data],
    )

    page2 = PageData(markdown="# Page 2\n\nNo images here.", images=[])

    response = MistralOCRResponse(pages=[page1, page2])

    result = mistral_ocr._extract_markdown_with_images(response)

    expected = "# Page 1\n\nContent with \n**Image Description:**\nA beautiful sunset\n reference.\n---\n# Page 2\n\nNo images here."
    assert result == expected


def test_extract_markdown_with_images_none_markdown(mistral_ocr):
    page1 = PageData(markdown=None, images=[])
    page2 = PageData(markdown="# Valid content", images=[])

    response = MistralOCRResponse(pages=[page1, page2])

    result = mistral_ocr._extract_markdown_with_images(response)

    assert result == "# Valid content"


def test_extract_markdown_with_images_multiple_images(mistral_ocr):
    image1 = ImageData(
        id="img_1", image_annotation='{"description": "First image description"}'
    )
    image2 = ImageData(
        id="img_2", image_annotation='{"description": "Second image description"}'
    )

    page = PageData(
        markdown="Here is an image: ![img_1](img_1) and another: ![img_2](img_2)",
        images=[image1, image2],
    )

    response = MistralOCRResponse(pages=[page])

    result = mistral_ocr._extract_markdown_with_images(response)

    expected = "Here is an image: \n**Image Description:**\nFirst image description\n and another: \n**Image Description:**\nSecond image description\n"
    assert result == expected


def test_extract_markdown_with_images_no_matches(mistral_ocr):
    image = ImageData(
        id="img_1", image_annotation='{"description": "This won\'t be used"}'
    )

    page = PageData(markdown="No image references here", images=[image])

    response = MistralOCRResponse(pages=[page])

    result = mistral_ocr._extract_markdown_with_images(response)

    assert result == "No image references here"


def test_extract_image_description_valid_json(mistral_ocr):
    annotation = '{"description": "A detailed image description"}'

    result = mistral_ocr._extract_image_description(annotation)

    assert result == "A detailed image description"


def test_extract_image_description_empty_annotation(mistral_ocr):
    result = mistral_ocr._extract_image_description("")

    assert result == "Image description not available"


@pytest.mark.anyio
async def test_parse_handles_json_decode_error(
    mistral_ocr, mock_mistral_client, sample_pdf_data, mocker
):
    mocker.patch.object(mistral_ocr, "client", mock_mistral_client)

    mock_response = mocker.MagicMock()
    mock_response.model_dump_json.side_effect = Exception("JSON decode error")
    mock_mistral_client.ocr.process_async.return_value = mock_response

    with pytest.raises(Exception, match="JSON decode error"):
        await mistral_ocr.parse(sample_pdf_data, "application/pdf")


def test_extract_markdown_with_images_none_images(mistral_ocr):
    page = PageData(markdown="# Page content", images=None)

    response = MistralOCRResponse(pages=[page])

    result = mistral_ocr._extract_markdown_with_images(response)

    assert result == "# Page content"


def test_extract_markdown_with_images_image_with_none_annotation(mistral_ocr):
    image = ImageData(id="img_1", image_annotation=None)

    page = PageData(markdown="Here is an image: ![img_1](img_1)", images=[image])

    response = MistralOCRResponse(pages=[page])

    result = mistral_ocr._extract_markdown_with_images(response)

    expected = (
        "Here is an image: \n**Image Description:**\nImage description not available\n"
    )
    assert result == expected
