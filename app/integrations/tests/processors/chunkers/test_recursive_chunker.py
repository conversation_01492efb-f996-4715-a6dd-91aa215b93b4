import pytest

from app.integrations.processors.chunkers.recursive_chunker import Recursive<PERSON><PERSON><PERSON>


@pytest.fixture
def recursive_chunker():
    return RecursiveChunker()


def test_recursive_chunker_initialization(recursive_chunker):
    assert recursive_chunker.chunk_size == 512
    assert recursive_chunker.chunk_overlap == 50
    assert recursive_chunker.separators == ["\n\n", "\n", ". ", " ", ""]


def test_custom_recursive_chunker_initialization():
    chunker = RecursiveChunker(chunk_size=100, chunk_overlap=10, separators=[",", ";"])

    assert chunker.chunk_size == 100
    assert chunker.chunk_overlap == 10
    assert chunker.separators == [",", ";"]


def test_chunk_simple_text(recursive_chunker):
    text = "This is a simple text that should not be chunked."
    chunks = recursive_chunker.chunk(text)

    assert len(chunks) == 1
    assert chunks[0] == text


def test_chunk_success():
    chunker = RecursiveChunker(chunk_size=10, chunk_overlap=2)
    text = "abcdefghijklmnopqrstuvwxyz"
    chunks = chunker.chunk(text)

    assert len(chunks) == 3
    assert chunks[0] == "abcdefghij"
    assert chunks[1] == "ijklmnopqr"
    assert chunks[2] == "qrstuvwxyz"
