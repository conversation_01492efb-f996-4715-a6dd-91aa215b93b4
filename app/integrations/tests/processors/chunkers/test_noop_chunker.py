from app.integrations.processors.chunkers.noop_chunker import NoopChunker


def test_chunk_returns_single_chunk():
    chunker = NoopChunker()
    text = "Short text"
    result = chunker.chunk(text)
    assert result == [text]


def test_chunk_splits_long_text():
    chunker = NoopChunker(chunk_size=5)
    text = "This is a long text"
    result = chunker.chunk(text)
    expected = ["This ", "is a ", "long ", "text"]
    assert result == expected


def test_chunk_handles_empty_text():
    chunker = NoopChunker()
    text = ""
    result = chunker.chunk(text)
    assert result == [""]
