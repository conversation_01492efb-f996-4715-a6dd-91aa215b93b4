import uuid

import pytest
from sqlalchemy import select

from app.integrations.models import CRMAccountAccess
from app.integrations.schemas import CRMAccountAccessData, CRMAccountAccessSlice
from app.integrations.stores.pg_crm_store import PostgresCRMStore
from app.integrations.types import IntegrationSource


@pytest.fixture
def crm_store(async_db_session):
    return PostgresCRMStore(
        tenant_id=uuid.uuid4(),
        source=IntegrationSource.SALESFORCE,
        session=async_db_session,
    )


@pytest.mark.anyio
async def test_store_account_access(crm_store, async_db_session):
    access_slice = CRMAccountAccessSlice(
        user_id="test_user_1",
        accounts=[
            CRMAccountAccessData(
                account_id="account_1",
                account_name="Test Account 1",
                access_type="owner",
                access_role="Sales Manager",
            ),
            CRMAccountAccessData(
                account_id="account_2",
                account_name="Test Account 2",
                access_type="team",
                access_role="Account Member",
            ),
        ],
    )

    stored_count, deleted_count = await crm_store.store_account_access(access_slice)

    assert stored_count == 2
    assert deleted_count == 0

    stmt = select(CRMAccountAccess).filter_by(
        tenant_id=crm_store.tenant_id,
        source=crm_store.source,
        crm_user_id="test_user_1",
    )
    result = await async_db_session.execute(stmt)
    db_records = result.scalars().all()

    assert len(db_records) == 2
    assert {record.crm_account_id for record in db_records} == {
        "account_1",
        "account_2",
    }
    assert {record.crm_access_type for record in db_records} == {"owner", "team"}


@pytest.mark.anyio
async def test_store_account_access_replaces_existing(crm_store, async_db_session):
    initial_slice = CRMAccountAccessSlice(
        user_id="test_user_2",
        accounts=[
            CRMAccountAccessData(
                account_id="initial_account",
                account_name="Initial Account",
                access_type="owner",
                access_role="Initial Role",
            )
        ],
    )
    await crm_store.store_account_access(initial_slice)

    new_slice = CRMAccountAccessSlice(
        user_id="test_user_2",
        accounts=[
            CRMAccountAccessData(
                account_id="new_account_1",
                account_name="New Account 1",
                access_type="team",
                access_role="New Role 1",
            ),
            CRMAccountAccessData(
                account_id="new_account_2",
                account_name="New Account 2",
                access_type="territory",
                access_role="New Role 2",
            ),
        ],
    )

    stored_count, deleted_count = await crm_store.store_account_access(new_slice)

    assert stored_count == 2
    assert deleted_count == 1

    stmt = select(CRMAccountAccess).filter_by(
        tenant_id=crm_store.tenant_id,
        source=crm_store.source,
        crm_user_id="test_user_2",
    )
    result = await async_db_session.execute(stmt)
    db_records = result.scalars().all()

    assert len(db_records) == 2
    assert {record.crm_account_id for record in db_records} == {
        "new_account_1",
        "new_account_2",
    }


@pytest.mark.anyio
async def test_get_user_account_access(crm_store):
    initial_slice = CRMAccountAccessSlice(
        user_id="test_user_3",
        accounts=[
            CRMAccountAccessData(
                account_id="account_3_1",
                account_name="Test Account 3.1",
                access_type="owner",
                access_role="Sales Manager",
            ),
            CRMAccountAccessData(
                account_id="account_3_2",
                account_name="Test Account 3.2",
                access_type="team",
                access_role="Account Member",
            ),
        ],
    )
    await crm_store.store_account_access(initial_slice)

    retrieved_slice = await crm_store.get_user_account_access("test_user_3")

    assert retrieved_slice.user_id == "test_user_3"
    assert len(retrieved_slice.accounts) == 2

    retrieved_account_ids = {account.account_id for account in retrieved_slice.accounts}
    assert retrieved_account_ids == {"account_3_1", "account_3_2"}

    for account in retrieved_slice.accounts:
        if account.account_id == "account_3_1":
            assert account.access_type == "owner"
            assert account.access_role == "Sales Manager"
        elif account.account_id == "account_3_2":
            assert account.access_type == "team"
            assert account.access_role == "Account Member"


@pytest.mark.anyio
async def test_clear_user_account_access(crm_store, async_db_session):
    initial_slice = CRMAccountAccessSlice(
        user_id="test_user_4",
        accounts=[
            CRMAccountAccessData(
                account_id="account_4_1",
                account_name="Test Account 4.1",
                access_type="owner",
                access_role="Sales Manager",
            )
        ],
    )
    await crm_store.store_account_access(initial_slice)

    await crm_store.clear_user_account_access("test_user_4")

    stmt = select(CRMAccountAccess).filter_by(
        tenant_id=crm_store.tenant_id,
        source=crm_store.source,
        crm_user_id="test_user_4",
    )
    result = await async_db_session.execute(stmt)
    db_records = result.scalars().all()

    assert len(db_records) == 0


@pytest.mark.anyio
async def test_data_isolation_by_tenant_id(async_db_session):
    tenant_a = uuid.uuid4()
    tenant_b = uuid.uuid4()

    store_a = PostgresCRMStore(
        tenant_id=tenant_a,
        source=IntegrationSource.SALESFORCE,
        session=async_db_session,
    )
    store_b = PostgresCRMStore(
        tenant_id=tenant_b,
        source=IntegrationSource.SALESFORCE,
        session=async_db_session,
    )

    slice_a = CRMAccountAccessSlice(
        user_id="test_user_5",
        accounts=[
            CRMAccountAccessData(
                account_id="account_5_1",
                account_name="Test Account 5.1",
                access_type="owner",
                access_role="Sales Manager",
            )
        ],
    )
    await store_a.store_account_access(slice_a)

    retrieved_slice = await store_b.get_user_account_access("test_user_5")

    assert len(retrieved_slice.accounts) == 0


@pytest.mark.anyio
async def test_data_isolation_by_source(async_db_session):
    tenant_id = uuid.uuid4()

    store_salesforce = PostgresCRMStore(
        tenant_id=tenant_id,
        source=IntegrationSource.SALESFORCE,
        session=async_db_session,
    )
    store_hubspot = PostgresCRMStore(
        tenant_id=tenant_id,
        source=IntegrationSource.HUBSPOT,
        session=async_db_session,
    )

    slice_salesforce = CRMAccountAccessSlice(
        user_id="test_user_6",
        accounts=[
            CRMAccountAccessData(
                account_id="account_6_1",
                account_name="Test Account 6.1",
                access_type="owner",
                access_role="Sales Manager",
            )
        ],
    )
    await store_salesforce.store_account_access(slice_salesforce)

    retrieved_slice = await store_hubspot.get_user_account_access("test_user_6")

    assert len(retrieved_slice.accounts) == 0
