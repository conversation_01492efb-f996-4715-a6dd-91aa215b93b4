import uuid

import pytest
from sqlalchemy import select

from app.integrations.models.changelog_cursor import ChangelogCursor
from app.integrations.stores.pg_cursor_store import PostgresCursorStore


@pytest.fixture
def cursor_store(async_db_session):
    return PostgresCursorStore(
        tenant_id=uuid.uuid4(),
        session=async_db_session,
    )


@pytest.mark.anyio
async def test_get_position_new_cursor(cursor_store):
    position = await cursor_store.get_position("new_cursor_id")
    assert position == 0


@pytest.mark.anyio
async def test_update_position_new_cursor(cursor_store, async_db_session):
    new_position = 42
    cursor_id = "test_cursor"

    await cursor_store.update_position(cursor_id, new_position)

    stmt = select(ChangelogCursor).filter_by(
        cursor_id=cursor_id,
        tenant_id=cursor_store._tenant_id,
    )
    result = await async_db_session.execute(stmt)
    cursor = result.scalar_one_or_none()

    assert cursor is not None
    assert cursor.cursor_position == new_position
    assert cursor.cursor_id == cursor_id
    assert cursor.tenant_id == cursor_store._tenant_id


@pytest.mark.anyio
async def test_update_position_existing_cursor(cursor_store, async_db_session):
    initial_position = 10
    new_position = 42
    cursor_id = "test_cursor"

    initial_cursor = ChangelogCursor(
        cursor_id=cursor_id,
        tenant_id=cursor_store._tenant_id,
        cursor_position=initial_position,
    )
    async_db_session.add(initial_cursor)
    await async_db_session.commit()

    await cursor_store.update_position(cursor_id, new_position)

    stmt = select(ChangelogCursor).filter_by(
        cursor_id=cursor_id,
        tenant_id=cursor_store._tenant_id,
    )
    result = await async_db_session.execute(stmt)
    cursor = result.scalar_one_or_none()

    assert cursor is not None
    assert cursor.cursor_position == new_position
