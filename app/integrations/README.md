# App Integrations

A modular and extensible integration system for FastAPI that connects various external services (CRM, Messaging, File Storage) with a unified architecture.

## 🏗️ Architecture

The system follows a layered architecture using Factory and Adapter patterns:

```
Factory (create_factory) 
    ↓
IntegrationFactory 
    ↓
Handles (CRMHandle, MessagingHandle, FileHandle)
    ↓  
Backends (CRMBackend, MessagingBackend, FileBackend)
    ↓
Adapters (SalesforceAdapter, SlackAdapter, GCSAdapter)
    ↓
Clients (SalesforceClient, SlackClient, GCSClient)
```

### Directory Structure

```
app/integrations/
├── adapters/          # Service-specific implementations
│   ├── gcs/          # Google Cloud Storage
│   ├── salesforce/   # Salesforce CRM
│   └── slack/        # Slack Messaging
├── backends/          # Business logic by type
│   ├── crm/          # CRM operations
│   ├── file/         # File operations  
│   └── messaging/    # Messaging operations
├── base/              # Abstract classes and interfaces
├── models/            # SQLAlchemy models
├── processors/        # Data processing (embedders, indexers, readers)
├── stores/            # Persistence layers (PostgreSQL)
├── tests/             # Unit tests
├── context.py         # Integration context
├── factory.py         # Main factory
├── handles.py         # User-facing handles
├── schemas.py         # Pydantic data structures
└── types.py          # Types and enums
```

## 🎯 Core Concepts

### 1. **Adapters**
Low-level interfaces to external APIs. Each adapter handles authentication, API calls, and data transformation for a specific service.

### 2. **Backends** 
Business logic layer that orchestrates adapters and provides high-level operations. Contains service-agnostic business rules.

### 3. **Handles**
User-facing interfaces that expose backend functionality. These are what developers interact with directly.

### 4. **Factory**
Create and configure the appropriate instances based on integration source and type.

### 5. **Credentials Resolver**
Manages authentication credentials for different services, including token refresh logic.

## 🚀 Quick Start

### Basic Usage

```python
import uuid
from app.integrations.factory import create_factory
from app.integrations.types import IntegrationSource

# Create a factory for a tenant
tenant_id = uuid.uuid4()
factory = create_factory(
    tenant_id=tenant_id,
    credentials_resolver=your_credentials_resolver,
    db_session_factory=your_db_session_factory
)

# Get a CRM handle for Salesforce
crm = factory.crm(IntegrationSource.SALESFORCE)

# Use the CRM operations
account = await crm.get_account("account_id")
opportunities = await crm.list_opportunities_by_account("account_id")
```



## 📋 Supported Integrations

### CRM Systems
- **Salesforce**: Full CRM operations (accounts, opportunities, contacts, tasks, events)

### Messaging Platforms  
- **Slack**: Channel ingestion, message processing, search capabilities

### File Storage
- **Google Cloud Storage**: File upload, download, delete operations

## 🔧 Integration Types

### CRM Integration

```python
# Get CRM handle
crm = factory.crm(IntegrationSource.SALESFORCE)

# Account operations
account = await crm.get_account("account_id")
updated_account = await crm.update_account("account_id", {"Name": "New Name"})
accounts = await crm.search_accounts({"Name": "ACME"})

# Opportunity operations
opportunity = await crm.get_opportunity("opp_id")
opportunities = await crm.list_opportunities_by_account("account_id")

# Contact operations
contact = await crm.create_contact({
    "FirstName": "John",
    "LastName": "Doe",
    "Email": "<EMAIL>"
})

# Bulk sync account access
result = await crm.bulk_sync_account_access(
    crm_user_ids=["user1", "user2"],
    interval_seconds=300,
    daemon_mode=False
)
```

### Messaging Integration

```python
# Get messaging handle  
messaging = factory.messaging(IntegrationSource.SLACK)

# Search messages in a channel
results = await messaging.search_channel_messages(
    channel_id="C1234567890",
    query="important meeting",
    limit=10
)

# Start channel ingestion (background process)
ingestion_result = await messaging.start_channel_ingestion(
    channel_ids=["C1234567890", "C0987654321"],
    interval_seconds=300,
    lookback_days=7,
    daemon_mode=True
)

# Start message processing (background process)
processing_result = await messaging.start_channel_processing(
    channel_ids=["C1234567890"],
    interval_seconds=300,
    daemon_mode=False
)
```

### File Integration

```python
# Get file handle
file_storage = factory.file(IntegrationSource.GCS)

# Upload a file
await file_storage.upload_file(
    bucket_name="my-bucket",
    file_obj=file_object,
    file_name="document.pdf"
)
```

## 🔐 Credentials Management

The system uses a credentials resolver pattern to handle authentication:

### Implementing ICredentialsResolver

```python
from app.integrations.base.credentials_resolver import ICredentialsResolver, ICredentials
from app.integrations.types import IntegrationSource

class MyCredentialsResolver(ICredentialsResolver):
    async def get_credentials(self, source: IntegrationSource) -> ICredentials:
        if source == IntegrationSource.SALESFORCE:
            return SalesforceCredentials(
                username="<EMAIL>",
                password="password",
                security_token="token"
            )
        elif source == IntegrationSource.SLACK:
            return SlackCredentials(
                token="xoxb-your-slack-token"
            )
        # ... other sources
```

### Implementing ICredentials

```python
class SalesforceCredentials(ICredentials):
    def __init__(self, username: str, password: str, security_token: str):
        self._secrets = {
            "username": username,
            "password": password,
            "security_token": security_token
        }
    
    @property
    def secrets(self) -> dict[str, Any]:
        return self._secrets
    
    async def refresh_token(self) -> "ICredentials":
        # Implement token refresh logic
        return self
```

## 🔄 Background Processing

The system supports background processing for long-running operations:

### Messaging Ingestion Pipeline

```python
# Start ingestion in daemon mode (runs continuously)
await messaging.start_channel_ingestion(
    channel_ids=["C1234567890"],
    interval_seconds=300,  # Check every 5 minutes
    lookback_days=7,       # Look back 7 days for messages
    daemon_mode=True       # Run continuously
)
```

### CRM Sync Pipeline

```python
# Bulk sync account access for multiple users
await crm.bulk_sync_account_access(
    crm_user_ids=["user1", "user2", "user3"],
    interval_seconds=1800,  # Sync every 30 minutes
    daemon_mode=True        # Run continuously
)
```

## 🏪 Data Storage

The system uses PostgreSQL for data persistence with the following stores:

- **PostgresMessageStore**: Stores messaging data with changelog tracking
- **PostgresDocumentStore**: Stores processed documents with vector embeddings
- **PostgresCRMStore**: Stores CRM account access data
- **PostgresCursorStore**: Tracks processing positions for changelog consumers

### Database Sessions

```python
# Using existing session
factory = create_factory(
    tenant_id=tenant_id,
    credentials_resolver=credentials_resolver,
    db_session=existing_session  # Shared session
)

# Using session factory
factory = create_factory(
    tenant_id=tenant_id,
    credentials_resolver=credentials_resolver,
    db_session_factory=lambda: AsyncSession(engine)  # New session per operation
)
```

## 🔧 Extending the System

### Adding a New Integration

1. **Create the client** (low-level API wrapper):
```python
# app/integrations/adapters/hubspot/client.py
class HubSpotClient:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.hubapi.com"
    
    def get_contact(self, contact_id: str) -> dict:
        # HubSpot API implementation
        pass
    
    def get_deal(self, deal_id: str) -> dict:
        # HubSpot API implementation
        pass
```

2. **Create the adapter** (implements base interface):
```python
# app/integrations/adapters/hubspot/adapter.py
from app.integrations.base.crm_adapter import BaseCRMAdapter

class HubSpotAdapter(BaseCRMAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self._client = HubSpotClient(credentials.secrets["api_key"])
    
    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.HUBSPOT
    
    async def get_account(self, account_id: str) -> dict[str, Any]:
        return self._client.get_contact(account_id)
    
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return self._client.get_deal(opportunity_id)
    
    # ... implement other BaseCRMAdapter methods
```

3. **Register in factory**:
```python
# app/integrations/factory.py
@staticmethod
def _get_integration_mapping():
    return {
        IntegrationSource.HUBSPOT: {
            BackendType.CRM: (HubSpotAdapter, CRMBackend),
        },
        # ... existing mappings
    }
```

### Adding a New Backend Type

1. **Create base backend interface**:
```python
# app/integrations/base/sales_analytics_backend.py
class BaseSalesAnalyticsBackend(BaseBackend, ABC):
    @property
    def backend_type(self) -> BackendType:
        return BackendType.SALES_ANALYTICS
    
    @abstractmethod
    async def generate_sales_report(self, report_type: str, date_range: tuple) -> dict:
        pass
    
    @abstractmethod
    async def get_pipeline_metrics(self, user_id: str) -> dict:
        pass
    
    @abstractmethod
    async def calculate_conversion_rates(self, time_period: str) -> dict:
        pass
```

2. **Implement concrete backend**:
```python
# app/integrations/backends/sales_analytics/backend.py
class SalesAnalyticsBackend(BaseSalesAnalyticsBackend):
    async def generate_sales_report(self, report_type: str, date_range: tuple) -> dict:
        adapter = await self.get_adapter()
        return await adapter.generate_sales_report(report_type, date_range)
    
    async def get_pipeline_metrics(self, user_id: str) -> dict:
        adapter = await self.get_adapter()
        opportunities = await adapter.list_opportunities_by_user(user_id)
        # Calculate metrics from opportunities
        return self._calculate_pipeline_metrics(opportunities)
```

3. **Create handle**:
```python
# app/integrations/handles.py
class SalesAnalyticsHandle(BaseHandle):
    async def generate_sales_report(self, report_type: str, date_range: tuple) -> dict:
        return await self._backend.generate_sales_report(report_type, date_range)
    
    async def get_pipeline_metrics(self, user_id: str) -> dict:
        return await self._backend.get_pipeline_metrics(user_id)
```

---

This integration system provides a solid foundation for connecting multiple external services with a consistent, maintainable architecture. The modular design makes it easy to add new integrations and extend functionality as needed.