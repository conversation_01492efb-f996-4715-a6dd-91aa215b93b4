import datetime
import hashlib
from uuid import UUID

from sqlalchemy import Delete, Select, Update, delete, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.integrations.base.message_store import IMessageStore
from app.integrations.models.message_changelog import MessageChangelog
from app.integrations.models.message_raw_data import MessageRawData
from app.integrations.schemas import (
    ChannelDataSlice,
    MessageChangelogData,
    MessageData,
    ReconciliationStats,
)
from app.integrations.types import IntegrationSource


class PostgresMessageStore(IMessageStore):
    def __init__(
        self,
        tenant_id: UUID,
        source: IntegrationSource,
        session: AsyncSession,
        with_changelog: bool = True,
    ) -> None:
        self.tenant_id = tenant_id
        self.source = source
        self.session = session
        self.with_changelog = with_changelog

    async def upsert_message(self, data: MessageData) -> None:
        """
        Store or update a message in the store system.

        This method handles deduplication and updates:
          - If a message with the same message_id and tenant_id does not exist:
              - Insert the new message.
          - If the message exists:
              - If the content has changed or the tombstone flag has changed,
                update the record accordingly.

        Args:
            data: The message data to store or update

        Note:
            The unique message_id must be provided by the caller.
            This implementation does not handle race conditions and may not be fully performant.
        """
        message_id = data.message_id
        channel_id = data.channel_id
        tenant_id = self.tenant_id
        source = self.source
        content_hash = self._compute_hash(data)

        operation = None

        stmt = self._build_scoped_stmt(
            MessageRawData,
            select(MessageRawData),
            MessageRawData.message_id == message_id,
            MessageRawData.channel_id == channel_id,
        )

        result = await self.session.execute(stmt)
        existing: MessageRawData | None = result.scalar_one_or_none()

        if existing is None:
            operation = MessageChangelog.Operation.INSERT
            new_record = MessageRawData(
                message_id=message_id,
                channel_id=channel_id,
                raw_data=data.model_dump_json(),
                hash=content_hash,
                author=data.author,
                thread_id=data.thread_id,
                parent_id=data.parent_id,
                tombstone=data.tombstone,
                tenant_id=tenant_id,
                source=source,
                sent_at=data.sent_at,
                last_edit_at=data.last_edit_at,
            )
            self.session.add(new_record)
        else:
            if existing.hash != content_hash:
                operation = MessageChangelog.Operation.UPDATE
                existing.raw_data = data.model_dump_json()
                existing.hash = content_hash
                existing.sent_at = data.sent_at
                existing.last_edit_at = data.last_edit_at
                existing.author = data.author
                existing.thread_id = data.thread_id
                existing.parent_id = data.parent_id
                existing.tombstone = data.tombstone or False

        if operation:
            await self._log_operation(
                operation=operation, message_id=message_id, channel_id=channel_id
            )

        await self.session.commit()

    async def delete_message(self, message_id: str) -> bool:
        """
        Delete a message from the database and return its channel_id.

        Args:
            message_id: The unique identifier of the message to delete

        Returns:
            True if a message was deleted, False otherwise
        """
        # Delete the message and retrieve its channel_id in a single operation
        stmt = self._build_scoped_stmt(
            MessageRawData,
            delete(MessageRawData),
            MessageRawData.message_id == message_id,
        ).returning(MessageRawData.channel_id)

        # Execute the statement and get the result
        result = await self.session.execute(stmt)
        row = result.first()

        if row is None:
            return False

        channel_id = row[0]

        # Log the deletion
        await self._log_operation(
            operation=MessageChangelog.Operation.DELETE,
            message_id=message_id,
            channel_id=channel_id,
        )
        await self.session.commit()

        return True

    async def get_message(self, message_id: str) -> MessageData | None:
        """
        Retrieve a stored message by its unique message ID.

        Args:
            message_id: The unique identifier of the message to retrieve

        Returns:
            A MessageData instance if found, or None otherwise
        """
        stmt = self._build_scoped_stmt(
            MessageRawData,
            select(MessageRawData),
            MessageRawData.message_id == message_id,
        )

        result = await self.session.execute(stmt)
        record = result.scalar_one_or_none()
        if record is None:
            return None
        return MessageData.model_validate_json(record.raw_data)

    async def get_message_by_id(self, id: UUID) -> MessageData | None:
        """
        Retrieve a stored message by its primary key (ID) in the database.

        Args:
            id: The primary key (UUID) of the record in the MessageRawData table

        Returns:
            A MessageData instance if found, or None otherwise
        """
        stmt = self._build_scoped_stmt(
            MessageRawData,
            select(MessageRawData),
            MessageRawData.id == id,
        )

        result = await self.session.execute(stmt)
        record = result.scalar_one_or_none()
        if record is None:
            return None
        return MessageData.model_validate_json(record.raw_data)

    async def get_thread_replies(
        self, channel_id: str, message_id: str
    ) -> list[MessageData]:
        """
        Gets all replies for a thread in a channel
        """
        stmt = self._build_scoped_stmt(
            MessageRawData,
            select(MessageRawData.raw_data),
            MessageRawData.channel_id == channel_id,
            MessageRawData.thread_id == message_id,
            MessageRawData.message_id != message_id,
        )

        result = await self.session.execute(stmt)
        records = result.mappings()
        return [
            MessageData.model_validate_json(record["raw_data"]) for record in records
        ]

    async def get_main_messages_before(
        self,
        channel_id: str,
        message_id: str,
        limit: int,
    ) -> list[MessageData]:
        """
        Gets main messages (not replies) that came before the given message in a channel
        """
        records = await self._get_main_messages_around(
            channel_id, message_id, limit, after=False
        )
        return [
            MessageData.model_validate_json(record["raw_data"]) for record in records
        ]

    async def get_main_messages_after(
        self,
        channel_id: str,
        message_id: str,
        limit: int,
    ) -> list[MessageData]:
        """
        Gets main messages (not replies) that came after the given message in a channel
        """
        records = await self._get_main_messages_around(
            channel_id, message_id, limit, after=True
        )
        return [
            MessageData.model_validate_json(record["raw_data"]) for record in records
        ]

    async def reconcile_channel_messages(
        self, source_slice: ChannelDataSlice
    ) -> ReconciliationStats:
        """
        Perform a full reconciliation between source messages and stored messages.

        This method serves as the main entry point for the reconciliation process.
        It computes the differences between the source data and stored data,
        then applies those differences to the database.

        Args:
            source_slice: A slice of messages from the external source, containing
                         the channel ID and message data
        """
        (
            messages_to_insert,
            messages_to_update,
            message_ids_to_delete,
        ) = await self._diff_messages(source_slice=source_slice)

        await self._apply_diff(
            source_slice.channel_id,
            messages_to_insert,
            messages_to_update,
            message_ids_to_delete,
        )

        return ReconciliationStats(
            inserts=len(messages_to_insert),
            updates=len(messages_to_update),
            deletes=len(message_ids_to_delete),
        )

    async def get_channel_changelog(
        self,
        channel_id: str,
        last_cursor_position: int,
        batch_size: int = 100,
    ) -> list[MessageChangelogData]:
        """
        Retrieve changelog for a specific channel.

        Args:
            channel_id: Identifier of the channel
            last_cursor_position: From cursor
            batch_size: Maximum number of changes to retrieve

        Returns:
            List of changes
        """
        stmt = self._build_scoped_stmt(
            MessageChangelog,
            select(MessageChangelog),
            MessageChangelog.channel_id == channel_id,
            MessageChangelog.cursor_id > last_cursor_position,
        )
        stmt = stmt.order_by(MessageChangelog.cursor_id).limit(batch_size)

        # Execute query
        result = await self.session.execute(stmt)
        changes = result.scalars().all()

        return [
            MessageChangelogData(
                cursor_id=change.cursor_id,
                operation=change.operation,
                message_id=change.message_id,
                channel_id=change.channel_id,
                created_at=change.created_at,
            )
            for change in changes
        ]

    def _compute_hash(self, message: MessageData) -> str:
        hash_input = f"{message.content}|{message.tombstone}"
        return hashlib.sha256(hash_input.encode("utf-8")).hexdigest()

    async def _diff_messages(
        self,
        source_slice: ChannelDataSlice,
    ) -> tuple[list[MessageData], list[MessageData], list[str]]:
        """
        Compute the differences between source messages and stored messages.

        This method performs a two-pass reconciliation process:
        1. First pass: Identifies messages to insert, update, or delete by comparing source messages with DB
        2. Second pass: Identifies stale messages in the DB that are not present in the source

        Args:
            source_slice: Messages retrieved from external source

        Returns:
            Tuple[messages_to_insert, messages_to_update, message_ids_to_delete]:
            - messages_to_insert: New messages to add to the database
            - messages_to_update: Existing messages that need updating
            - message_ids_to_delete: IDs of messages to remove
        """
        # Initialize collections for tracking differences
        messages_to_insert: list[MessageData] = []
        messages_to_update: list[MessageData] = []
        message_ids_to_delete: list[str] = []

        # First pass: Check source messages against db messages
        await self._find_inserts_and_updates(
            source_slice.messages,
            messages_to_insert,
            messages_to_update,
        )

        # Second pass: Check db messages against source messages
        await self._find_deletions(
            source_slice,
            message_ids_to_delete,
        )

        return messages_to_insert, messages_to_update, message_ids_to_delete

    async def _apply_diff(
        self,
        channel_id: str,
        messages_to_insert: list[MessageData],
        messages_to_update: list[MessageData],
        message_ids_to_delete: list[str],
    ) -> None:
        """
        Apply the differences identified by _diff_messages to the database.

        This method handles:
        - Insertion of new messages
        - Updates to existing messages
        - Deletion of stale messages

        Args:
            channel_id: Messages channel ID
            messages_to_insert: New messages to add to the database
            messages_to_update: Existing messages that need updating
            message_ids_to_delete: IDs of messages to remove
        """
        # Handle insertions and updates
        for msg in messages_to_insert + messages_to_update:
            await self.upsert_message(msg)

        # Handle deletions
        if message_ids_to_delete:
            await self._delete_messages_from_db(message_ids_to_delete)

            # Log each deletion in the changelog
            for message_id in message_ids_to_delete:
                await self._log_operation(
                    operation=MessageChangelog.Operation.DELETE,
                    message_id=message_id,
                    channel_id=channel_id,
                )

        await self.session.commit()

    async def _find_inserts_and_updates(
        self,
        source_messages: list[MessageData],
        messages_to_insert: list[MessageData],
        messages_to_update: list[MessageData],
    ) -> None:
        """
        Identify which messages need to be inserted or updated.

        For each message in the source:
        - If the message doesn't exist in DB, mark it for insertion
        - If the message exists but has changed content or tombstone status, mark it for update

        Args:
            source_messages: A slice of messages from the external source
            messages_to_insert: List to populate with messages to insert
            messages_to_update: List to populate with messages to update
        """
        for source_msg in source_messages:
            msg_id = source_msg.message_id

            # Check if the message exists in the database
            db_msg = await self._get_message_from_db(msg_id)

            # Message doesn't exist in database
            if not db_msg:
                messages_to_insert.append(source_msg)
                continue

            # Message exists but has changed
            if db_msg["hash"] != self._compute_hash(source_msg):
                messages_to_update.append(source_msg)

    async def _find_deletions(
        self,
        source_slice: ChannelDataSlice,
        messages_to_delete: list[str],
    ) -> None:
        """
        Identify messages in the database that need to be deleted.

        This method finds messages that are in the database but not in the source,
        including:
        1. Stale main messages (thread roots or standalone) and all their replies
        2. Stale replies to existing main messages

        Args:
            source_slice: A slice of messages from the external source
            messages_to_delete: List to populate with message IDs to delete
        """
        channel_id = source_slice.channel_id

        # Extract the main message IDs from the source
        source_main_message_ids = {
            msg.message_id
            for msg in source_slice.messages
            if msg.thread_id is None or msg.message_id == msg.thread_id
        }

        # Retrieve stale main messages from the database for the same source time window
        stale_main_message_ids = await self._get_stale_main_message_ids_from_db(
            source_main_message_ids,
            channel_id,
            source_slice.from_time,
            source_slice.to_time,
        )

        # Retrieve all messages linked to these stale main messages
        if stale_main_message_ids:
            stale_thread_message_ids = await self._get_thread_message_ids_from_db(
                stale_main_message_ids, channel_id
            )
            messages_to_delete.extend(stale_thread_message_ids)

        # For each existing main message, check for stale replies
        for main_message_id in source_main_message_ids:
            # Get the source reply IDs for this main message
            source_reply_ids = {
                msg.message_id
                for msg in source_slice.messages
                if msg.thread_id == main_message_id
            }

            # Retrieve stale replies from the database
            stale_reply_ids = await self._get_stale_reply_ids_from_db(
                main_message_id, source_reply_ids
            )

            # Add stale replies to the deletion list
            messages_to_delete.extend(stale_reply_ids)

    def _build_scoped_stmt(
        self,
        model: type[MessageRawData] | type[MessageChangelog],
        stmt: Select | Delete | Update,
        *conditions,
    ):
        return stmt.where(
            model.tenant_id == self.tenant_id,
            model.source == self.source,
            *conditions,
        )

    async def _get_message_from_db(self, message_id: str) -> dict | None:
        stmt = self._build_scoped_stmt(
            MessageRawData,
            select(
                MessageRawData.message_id,
                MessageRawData.channel_id,
                MessageRawData.thread_id,
                MessageRawData.parent_id,
                MessageRawData.sent_at,
                MessageRawData.hash,
                MessageRawData.tombstone,
            ),
            MessageRawData.message_id == message_id,
        )

        result = await self.session.execute(stmt)
        result_row = result.mappings().first()

        if result_row is None:
            return None

        return dict(result_row)

    async def _delete_messages_from_db(self, message_ids: list[str]) -> int:
        stmt = self._build_scoped_stmt(
            MessageRawData,
            delete(MessageRawData),
            MessageRawData.message_id.in_(message_ids),
        )

        result = await self.session.execute(stmt)

        return result.rowcount

    async def _get_main_messages_around(
        self,
        channel_id: str,
        message_id: str,
        limit: int,
        after: bool = False,
    ) -> list[dict]:
        """
        Internal method to get main messages around a reference message.
        Returns list of dicts with only the needed fields.
        """
        # todo: we should also check that message_id is a main message
        reference_date = (
            select(MessageRawData.sent_at)
            .where(
                MessageRawData.message_id == message_id,
                # Explicit filters required here because we're using scalar_subquery
                MessageRawData.tenant_id == self.tenant_id,
                MessageRawData.source == self.source,
            )
            .scalar_subquery()
        )

        stmt = (
            self._build_scoped_stmt(
                MessageRawData,
                select(
                    MessageRawData.message_id,
                    MessageRawData.raw_data,
                ),
                MessageRawData.channel_id == channel_id,
                MessageRawData.sent_at >= reference_date
                if after
                else MessageRawData.sent_at <= reference_date,
                MessageRawData.message_id != message_id,
                (
                    MessageRawData.thread_id.is_(None)
                    | (MessageRawData.thread_id == MessageRawData.message_id)
                ),
            )
            .order_by(
                MessageRawData.sent_at.asc() if after else MessageRawData.sent_at.desc()
            )
            .limit(limit)
        )

        result = await self.session.execute(stmt)
        return [dict(row) for row in result.mappings()]

    async def _get_stale_main_message_ids_from_db(
        self,
        source_main_message_ids: set[str],
        channel_id: str,
        from_time: datetime.datetime,
        to_time: datetime.datetime,
    ) -> set[str]:
        stmt = self._build_scoped_stmt(
            MessageRawData,
            select(MessageRawData.message_id),
            MessageRawData.channel_id == channel_id,
            MessageRawData.message_id.notin_(list(source_main_message_ids)),
            MessageRawData.sent_at >= from_time,
            MessageRawData.sent_at <= to_time,
            (
                MessageRawData.thread_id.is_(None)
                | (MessageRawData.thread_id == MessageRawData.message_id)
            ),
        )

        result = await self.session.execute(stmt)
        return set(result.scalars().all())

    async def _get_thread_message_ids_from_db(
        self, main_message_ids: set[str], channel_id: str
    ) -> list[str]:
        if not main_message_ids:
            return []

        # Retrieve all messages (main and replies) for these threads
        stmt = self._build_scoped_stmt(
            MessageRawData,
            select(MessageRawData.message_id),
            MessageRawData.channel_id == channel_id,
            (
                MessageRawData.message_id.in_(list(main_message_ids))
                | MessageRawData.thread_id.in_(list(main_message_ids))
            ),
        )

        result = await self.session.execute(stmt)
        return list(result.scalars().all())

    async def _get_stale_reply_ids_from_db(
        self, main_message_id: str, source_reply_ids: set[str]
    ) -> set[str]:
        stmt = self._build_scoped_stmt(
            MessageRawData,
            select(MessageRawData.message_id),
            MessageRawData.thread_id == main_message_id,
            MessageRawData.message_id.notin_(list(source_reply_ids)),
            MessageRawData.message_id != main_message_id,
        )

        result = await self.session.execute(stmt)
        return set(result.scalars().all())

    async def _log_operation(
        self, operation: MessageChangelog.Operation, message_id: str, channel_id: str
    ) -> None:
        if not self.with_changelog:
            return

        changelog_entry = MessageChangelog(
            operation=operation.value,
            message_id=message_id,
            channel_id=channel_id,
            tenant_id=self.tenant_id,
            source=self.source,
        )
        self.session.add(changelog_entry)
