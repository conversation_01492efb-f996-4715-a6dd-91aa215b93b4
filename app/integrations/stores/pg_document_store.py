from uuid import UUI<PERSON>

from pgvector.sqlalchemy import Vector
from sqlalchemy import (
    BindParameter,
    Delete,
    Float,
    Select,
    Update,
    bindparam,
    cast,
    delete,
    literal,
    select,
)
from sqlalchemy.ext.asyncio import AsyncSession

from app.integrations.base.document_store import IDocumentStore
from app.integrations.models.document import Document
from app.integrations.schemas import DocumentData
from app.integrations.types import IntegrationSource


class PostgresDocumentStore(IDocumentStore):
    def __init__(
        self, session: AsyncSession, tenant_id: UUID, source: IntegrationSource
    ):
        self.session = session
        self.tenant_id = tenant_id
        self.source = source

    async def store_document(
        self, document: DocumentData, embedding: list[float] | None = None
    ) -> None:
        stmt = self._build_scoped_stmt(
            select(Document),
            Document.document_id == document.id,
        )
        result = await self.session.execute(stmt)
        existing: Document = result.scalar_one_or_none()

        tags = list(document.tags) if document.tags else []
        if existing:
            existing.content = document.content
            existing.source_timestamp = document.source_timestamp
            existing.tags = tags
            if embedding is not None:
                existing.embedding = embedding
        else:
            new_doc = Document(
                tenant_id=self.tenant_id,
                source=self.source,
                document_id=document.id,
                content=document.content,
                source_timestamp=document.source_timestamp,
                tags=tags,
                embedding=embedding,
            )
            self.session.add(new_doc)

        await self.session.commit()

    async def get_document(self, document_id: str) -> DocumentData | None:
        stmt = self._build_scoped_stmt(
            select(Document),
            Document.document_id == document_id,
        )
        result = await self.session.execute(stmt)
        document = result.scalar_one_or_none()

        if not document:
            return None

        return DocumentData(
            id=str(document.id),
            content=str(document.content),
            source_timestamp=document.source_timestamp,
            tags=set(document.tags) if document.tags else set(),
        )

    async def delete_document(self, document_id: str) -> None:
        delete_stmt = self._build_scoped_stmt(
            delete(Document),
            Document.document_id == document_id,
        )
        await self.session.execute(delete_stmt)
        await self.session.commit()

    async def find_document_ids_by_tag(self, tag: str) -> set[str]:
        stmt = self._build_scoped_stmt(
            select(Document),
            Document.tags.any(literal(tag)),
        )
        result = await self.session.execute(stmt)
        results = result.scalars().all()

        return {doc.document_id for doc in results}

    async def find_similar_documents(
        self, embedding: list[float], limit: int = 10, tag_filter: str | None = None
    ) -> list[tuple[DocumentData, float]]:
        embedding_param: BindParameter[Vector] = bindparam(
            "embedding", value=embedding, type_=Vector
        )
        distance = Document.embedding.op("<->")(embedding_param)

        stmt = self._build_scoped_stmt(
            select(Document, cast(distance, Float).label("score")).order_by(distance)
        )

        if tag_filter:
            stmt = stmt.where(Document.tags.any(literal(tag_filter)))

        stmt = stmt.limit(limit)
        result = await self.session.execute(stmt)
        results = result.all()

        return [
            (
                DocumentData(
                    id=doc.document_id,
                    content=doc.content,
                    source_timestamp=doc.source_timestamp,
                    tags=set(doc.tags) if doc.tags else set(),
                ),
                score,
            )
            for doc, score in results
        ]

    def _build_scoped_stmt(self, stmt: Select | Delete | Update, *conditions):
        return stmt.where(
            Document.tenant_id == self.tenant_id,
            Document.source == self.source,
            *conditions,
        )
