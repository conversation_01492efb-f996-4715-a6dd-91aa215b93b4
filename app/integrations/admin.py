from typing import cast

from wtforms import <PERSON>Field

from app.admin import BaseModelView
from app.integrations.base.tenant_model import TenantModel
from app.integrations.models import (
    ChangelogCursor,
    CRMAccountAccess,
    CRMAccountAccessSyncRun,
    Document,
    FileProcessingRun,
    MessageChangelog,
    MessageRawData,
    MessagingIngestionRun,
    MessagingProcessingRun,
)
from app.integrations.types import IntegrationSource


class _IntegrationsModelView(BaseModelView):
    category = "Integrations"
    column_formatters = {
        "tenant_id": lambda m, _: f"{str(cast('TenantModel', m).tenant_id)[:5]}..."
    }
    form_overrides = {"source": SelectField}
    form_args = {
        "source": {
            "choices": [(e.value, e.name) for e in IntegrationSource],
            "coerce": IntegrationSource,
        }
    }


class _MessageIntegrationsModelView(_IntegrationsModelView):
    category = "Integrations / Message"


class _CrmIntegrationsModelView(_IntegrationsModelView):
    category = "Integrations / CRM"


class _FileIntegrationsModelView(_IntegrationsModelView):
    category = "Integrations / File"


class MessageRawDataAdmin(_MessageIntegrationsModelView, model=MessageRawData):
    autodiscover_order = 10
    column_list = [
        MessageRawData.tenant_id,
        MessageRawData.source,
        MessageRawData.channel_id,
        MessageRawData.message_id,
        MessageRawData.thread_id,
        MessageRawData.parent_id,
        "is_reply",
        MessageRawData.tombstone,
        MessageRawData.sent_at,
        MessageRawData.last_edit_at,
    ]
    column_searchable_list = [MessageRawData.message_id, MessageRawData.thread_id]
    column_default_sort = (MessageRawData.sent_at, True)


class MessageChangelogAdmin(_MessageIntegrationsModelView, model=MessageChangelog):
    autodiscover_order = 15
    column_list = [
        MessageChangelog.tenant_id,
        MessageChangelog.cursor_id,
        MessageChangelog.source,
        MessageChangelog.channel_id,
        MessageChangelog.message_id,
        "operation_label",
        MessageChangelog.created_at,
    ]
    column_default_sort = (MessageChangelog.cursor_id, True)
    column_searchable_list = [MessageChangelog.message_id]
    form_overrides = {
        "operation": SelectField,
        **_MessageIntegrationsModelView.form_overrides,
    }
    form_args = {
        "operation": {
            "choices": [(e.value, e.name) for e in MessageChangelog.Operation],
            "coerce": MessageChangelog.Operation,
        },
        **_MessageIntegrationsModelView.form_args,
    }


class ChangelogCursorAdmin(_MessageIntegrationsModelView, model=ChangelogCursor):
    autodiscover_order = 20
    column_list = [
        ChangelogCursor.tenant_id,
        ChangelogCursor.cursor_id,
        ChangelogCursor.cursor_position,
    ]
    column_default_sort = (MessageChangelog.updated_at, True)


class DocumentAdmin(_MessageIntegrationsModelView, model=Document):
    autodiscover_order = 25
    column_list = [
        Document.tenant_id,
        Document.source,
        Document.document_id,
        Document.source_timestamp,
    ]
    column_details_exclude_list = [Document.embedding]
    form_excluded_columns = [Document.embedding]
    column_default_sort = (Document.source_timestamp, True)
    column_searchable_list = [Document.document_id]


class MessagingIngestionRunAdmin(
    _MessageIntegrationsModelView, model=MessagingIngestionRun
):
    autodiscover_order = 30
    column_list = [
        MessagingIngestionRun.tenant_id,
        MessagingIngestionRun.source,
        MessagingIngestionRun.channel_id,
        MessagingIngestionRun.status,
        MessagingIngestionRun.run_start,
        MessagingIngestionRun.run_end,
        MessagingIngestionRun.messages_processed,
        MessagingIngestionRun.inserts,
        MessagingIngestionRun.updates,
        MessagingIngestionRun.deletes,
    ]
    column_default_sort = (MessagingIngestionRun.run_start, True)
    column_labels = {
        MessagingIngestionRun.messages_processed: "processed",
    }
    form_overrides = {"status": SelectField}
    form_args = {
        "status": {
            "choices": [(e.value, e.name) for e in MessagingIngestionRun.Status],
            "coerce": MessagingIngestionRun.Status,
        }
    }


class MessagingProcessingRunAdmin(
    _MessageIntegrationsModelView, model=MessagingProcessingRun
):
    autodiscover_order = 30
    column_list = [
        MessagingProcessingRun.tenant_id,
        MessagingProcessingRun.source,
        MessagingProcessingRun.channel_id,
        MessagingProcessingRun.status,
        MessagingProcessingRun.run_start,
        MessagingProcessingRun.run_end,
        MessagingProcessingRun.processed_changes,
        MessagingProcessingRun.regenerated_documents,
        MessagingProcessingRun.deleted_documents,
    ]
    column_default_sort = (MessagingProcessingRun.run_start, True)
    column_labels = {
        MessagingProcessingRun.processed_changes: "processed",
        MessagingProcessingRun.regenerated_documents: "regenerated docs",
        MessagingProcessingRun.deleted_documents: "deleted docs",
    }
    form_overrides = {"status": SelectField}
    form_args = {
        "status": {
            "choices": [(e.value, e.name) for e in MessagingProcessingRun.Status],
            "coerce": MessagingProcessingRun.Status,
        }
    }


class CRMAccountAccessAdmin(_CrmIntegrationsModelView, model=CRMAccountAccess):
    name = "CRM Account Access"
    name_plural = "CRM Account Access"
    autodiscover_order = 10
    column_list = [
        CRMAccountAccess.tenant_id,
        CRMAccountAccess.source,
        CRMAccountAccess.crm_user_id,
        CRMAccountAccess.crm_account_id,
        CRMAccountAccess.crm_account_name,
        CRMAccountAccess.crm_access_type,
        CRMAccountAccess.crm_access_role,
        CRMAccountAccess.created_at,
    ]
    column_default_sort = (MessageRawData.created_at, True)
    column_searchable_list = [
        CRMAccountAccess.crm_user_id,
        CRMAccountAccess.crm_account_id,
        CRMAccountAccess.crm_account_name,
    ]


class CRMAccessSyncRunAdmin(_CrmIntegrationsModelView, model=CRMAccountAccessSyncRun):
    name = "CRM Account Access"
    name_plural = "CRM Account Access Sync Runs"
    autodiscover_order = 20
    column_list = [
        CRMAccountAccessSyncRun.tenant_id,
        CRMAccountAccessSyncRun.source,
        CRMAccountAccessSyncRun.crm_user_id,
        CRMAccountAccessSyncRun.status,
        CRMAccountAccessSyncRun.run_start,
        CRMAccountAccessSyncRun.run_end,
        CRMAccountAccessSyncRun.new_access_count,
        CRMAccountAccessSyncRun.old_access_count,
    ]
    column_default_sort = (CRMAccountAccessSyncRun.run_start, True)
    form_overrides = {"status": SelectField}
    form_args = {
        "status": {
            "choices": [(e.value, e.name) for e in CRMAccountAccessSyncRun.Status],
            "coerce": CRMAccountAccessSyncRun.Status,
        }
    }


class FileProcessingRunAdmin(_FileIntegrationsModelView, model=FileProcessingRun):
    autodiscover_order = 30
    column_list = [
        FileProcessingRun.tenant_id,
        FileProcessingRun.source,
        FileProcessingRun.bucket_name,
        FileProcessingRun.extensions,
        FileProcessingRun.status,
        FileProcessingRun.files_processed,
        FileProcessingRun.deleted_documents,
        FileProcessingRun.run_start,
        FileProcessingRun.run_end,
    ]
    column_default_sort = (FileProcessingRun.run_start, True)
    column_labels = {
        FileProcessingRun.files_processed: "processed",
        FileProcessingRun.deleted_documents: "deleted docs",
    }
    form_overrides = {"status": SelectField}
    form_args = {
        "status": {
            "choices": [(e.value, e.name) for e in FileProcessingRun.Status],
            "coerce": FileProcessingRun.Status,
        }
    }
