import datetime
import enum

from sqlalchemy import AR<PERSON><PERSON>, DateTime, Integer, String
from sqlalchemy.orm import Mapped, mapped_column

from app.common.orm.types import StringEnum
from app.integrations.base.tenant_model import TenantModel
from app.integrations.types import IntegrationSource


class FileProcessingRun(TenantModel):
    __tablename__ = "file_processing_run"

    class Status(str, enum.Enum):
        IN_PROGRESS = "in_progress"
        SUCCESS = "success"
        FAILED = "failed"

    source: Mapped[IntegrationSource] = mapped_column(
        StringEnum(IntegrationSource), nullable=False
    )
    bucket_name: Mapped[str] = mapped_column(String, nullable=False)
    extensions: Mapped[list[str]] = mapped_column(ARRAY(String), nullable=False)
    status: Mapped[Status] = mapped_column(
        StringEnum(Status, length=20), nullable=False
    )
    files_processed: Mapped[int] = mapped_column(Integer, default=0)
    deleted_documents: Mapped[int] = mapped_column(Integer, default=0)
    error_message: Mapped[str | None] = mapped_column(String(1024), nullable=True)
    run_start: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False
    )
    run_end: Mapped[datetime.datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
