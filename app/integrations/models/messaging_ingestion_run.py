import datetime
import enum

from sqlalchemy import DateTime, Integer, String
from sqlalchemy.orm import Mapped, mapped_column

from app.common.orm.types import StringEnum
from app.integrations.base.tenant_model import TenantModel
from app.integrations.types import IntegrationSource


class MessagingIngestionRun(TenantModel):
    __tablename__ = "messaging_ingestion_run"

    class Status(str, enum.Enum):
        IN_PROGRESS = "in_progress"
        SUCCESS = "success"
        FAILED = "failed"

    source: Mapped[IntegrationSource] = mapped_column(
        StringEnum(IntegrationSource), nullable=False
    )
    channel_id: Mapped[str] = mapped_column(String, nullable=False)
    slice_from_time: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False
    )
    slice_to_time: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False
    )
    status: Mapped[Status] = mapped_column(
        StringEnum(Status, length=20), nullable=False
    )
    messages_processed: Mapped[int] = mapped_column(Integer, default=0)
    inserts: Mapped[int] = mapped_column(Integer, default=0)
    updates: Mapped[int] = mapped_column(Integer, default=0)
    deletes: Mapped[int] = mapped_column(Integer, default=0)
    error_message: Mapped[str | None] = mapped_column(String(1024), nullable=True)
    run_start: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False
    )
    run_end: Mapped[datetime.datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
