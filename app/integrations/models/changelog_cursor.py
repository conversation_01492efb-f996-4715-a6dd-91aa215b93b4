from sqlalchemy import BigInteger, String, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from app.integrations.base.tenant_model import TenantModel


class ChangelogCursor(TenantModel):
    __tablename__ = "changelog_cursor"

    cursor_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    cursor_position: Mapped[int] = mapped_column(BigInteger, nullable=False, default=0)

    __table_args__ = (
        UniqueConstraint("tenant_id", "cursor_id", name="uq_tenant_cursor_id"),
    )
