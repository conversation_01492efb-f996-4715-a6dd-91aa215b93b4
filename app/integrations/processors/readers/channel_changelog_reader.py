from dataclasses import dataclass

from app.integrations.base.cursor_store import ICursorStore
from app.integrations.base.message_store import IMessageStore
from app.integrations.schemas import MessageChangelogData


@dataclass
class MessageChangeSet:
    channel_id: str
    consumer_id: str
    changes: list[MessageChangelogData]


class ChannelChangelogReader:
    def __init__(
        self,
        message_store: IMessageStore,
        cursor_store: ICursorStore,
    ):
        self._store = message_store
        self._cursor_store = cursor_store

    async def get_next_changes(
        self, channel_id: str, consumer_id: str, batch_size: int = 100
    ) -> MessageChangeSet:
        cursor_id = self._build_cursor_id(consumer_id, channel_id)
        position = await self._cursor_store.get_position(cursor_id)
        changes = await self._store.get_channel_changelog(
            channel_id=channel_id,
            last_cursor_position=position,
            batch_size=batch_size,
        )
        return MessageChangeSet(
            channel_id=channel_id, consumer_id=consumer_id, changes=changes
        )

    async def ack(self, changeset: MessageChangeSet) -> None:
        if not changeset.changes:
            return
        cursor_id = self._build_cursor_id(changeset.consumer_id, changeset.channel_id)
        new_pos = changeset.changes[-1].cursor_id
        await self._cursor_store.update_position(cursor_id, new_pos)

    def _build_cursor_id(self, consumer_id: str, channel_id: str):
        return f"{consumer_id}:{channel_id}"
