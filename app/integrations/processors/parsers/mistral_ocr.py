import base64
import json

from mistralai import Mistral
from mistralai.extra import response_format_from_pydantic_model
from mistralai.models import DocumentURLChunk, ImageURLChunk
from pydantic import BaseModel

from app.common.helpers.logger import get_logger

logger = get_logger()


class Image(BaseModel):
    description: str


class ImageData(BaseModel):
    id: str
    image_annotation: str | None = None


class PageData(BaseModel):
    markdown: str | None = None
    images: list[ImageData] | None = None


class MistralOCRResponse(BaseModel):
    pages: list[PageData]


class MistralOCR:
    def __init__(self, api_key: str):
        self.client = Mistral(api_key=api_key)
        self.model = "mistral-ocr-latest"

    async def parse(self, file_data: bytes, content_type: str) -> str:
        """
        Parse PDF file using Mistral OCR.
        It will return a markdown text with properly positioned image descriptions.
        """
        file_base64 = base64.b64encode(file_data).decode("utf-8")
        document: ImageURLChunk | DocumentURLChunk

        if content_type.startswith("image/"):
            document = ImageURLChunk(
                type="image_url",
                image_url=f"data:{content_type};base64,{file_base64}",
            )
        else:
            document = DocumentURLChunk(
                type="document_url",
                document_url=f"data:{content_type};base64,{file_base64}",
            )

        response = await self.client.ocr.process_async(
            model=self.model,
            document=document,
            include_image_base64=False,
            bbox_annotation_format=response_format_from_pydantic_model(Image),
        )

        response_dict = json.loads(response.model_dump_json())
        typed_response = MistralOCRResponse.model_validate(response_dict)

        return self._extract_markdown_with_images(typed_response)

    def _extract_markdown_with_images(self, response: MistralOCRResponse) -> str:
        """
        Extract markdown text with properly positioned image descriptions.
        """
        page_contents = []

        for page in response.pages:
            markdown_content = page.markdown or ""

            if page.images:
                for img in page.images:
                    img_reference = f"![{img.id}]({img.id})"
                    if img_reference in markdown_content:
                        description = self._extract_image_description(
                            img.image_annotation
                        )
                        replacement = f"\n**Image Description:**\n{description}\n"
                        markdown_content = markdown_content.replace(
                            img_reference, replacement
                        )

            if markdown_content.strip():
                page_contents.append(markdown_content)

        return "\n---\n".join(page_contents)

    def _extract_image_description(self, annotation: str | None) -> str:
        """
        Extract image description from image annotation.
        """
        if not annotation:
            return "Image description not available"

        try:
            annotation_data = json.loads(annotation)
            return annotation_data.get("description", "Image description not available")
        except json.JSONDecodeError:
            logger.exception(f"Failed to parse image annotation: {annotation}")
            return "Image description not available"
