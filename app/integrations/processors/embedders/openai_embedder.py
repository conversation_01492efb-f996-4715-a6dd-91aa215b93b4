from enum import Enum

import openai
from openai import Async<PERSON>penA<PERSON>

from app.common.helpers.logger import get_logger
from app.integrations.base.embedder import IEmbedder

logger = get_logger()


class OpenAIEmbeddingModel(str, Enum):
    TEXT_EMBEDDING_3_SMALL = "text-embedding-3-small"
    TEXT_EMBEDDING_3_LARGE = "text-embedding-3-large"
    TEXT_EMBEDDING_3_ADA_002 = "text-embedding-ada-002"


class OpenAIEmbedder(IEmbedder):
    """
    OpenAI embedder implementation using the official OpenAI Python SDK.

    This embedder uses OpenAI's text-embedding-ada-002 model to generate embeddings.
    """

    def __init__(
        self,
        api_key: str,
        model: OpenAIEmbeddingModel = OpenAIEmbeddingModel.TEXT_EMBEDDING_3_SMALL,
    ):
        self.model = model
        self.client = AsyncOpenAI(api_key=api_key)

    async def embed_text(self, text: str) -> list[float]:
        if not text or not text.strip():
            raise ValueError("Text cannot be empty or None")

        try:
            response = await self.client.embeddings.create(
                model=self.model,
                input=text.strip(),
                encoding_format="float",
            )

            embedding = response.data[0].embedding

            return embedding

        except openai.APIError as e:
            logger.exception(f"OpenAI API error: {e}")
            raise Exception(f"Failed to generate embedding: {e}")
        except Exception as e:
            logger.exception(f"Unexpected error generating embedding: {e}")
            raise Exception(f"Failed to generate embedding: {e}")
