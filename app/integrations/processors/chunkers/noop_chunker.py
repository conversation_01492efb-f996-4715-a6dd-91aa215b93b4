from app.integrations.base.chunker import IChunker


class NoopChunker(IChunker):
    def __init__(self, chunk_size: int = 10):
        self.chunk_size = chunk_size

    def chunk(self, text: str) -> list[str]:
        if len(text) <= self.chunk_size:
            return [text]

        chunks = []
        for i in range(0, len(text), self.chunk_size):
            chunks.append(text[i : i + self.chunk_size])

        return chunks
