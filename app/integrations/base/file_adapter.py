from abc import ABC, abstractmethod
from typing import Binary<PERSON>

from app.integrations.base.adapter import BaseAdapter
from app.integrations.schemas import FileData
from app.integrations.types import ExtensionType


class BaseFileAdapter(BaseAdapter, ABC):
    @abstractmethod
    async def upload_file(
        self, bucket_name: str, file_obj: BinaryIO, file_name: str
    ) -> None:
        pass

    @abstractmethod
    async def download_file(self, bucket_name: str, file_name: str) -> bytes:
        pass

    @abstractmethod
    async def delete_file(self, bucket_name: str, file_name: str) -> None:
        pass

    @abstractmethod
    async def list_files(
        self, bucket_name: str, extensions: list[ExtensionType]
    ) -> list[FileData]:
        pass
