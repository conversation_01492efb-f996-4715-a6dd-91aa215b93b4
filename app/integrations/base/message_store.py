from abc import ABC, abstractmethod
from uuid import UUID

from app.integrations.schemas import (
    ChannelDataSlice,
    MessageChangelogData,
    MessageData,
    ReconciliationStats,
)


class IMessageStore(ABC):
    @abstractmethod
    async def upsert_message(self, data: MessageData) -> None:
        """
        Store a message in the store system.

        This method should handle deduplication and updates:
          - If the message does not exist, insert it.
          - If the message exists and its content has changed, update the record.
          - If the message is flagged as tombstone (data.tombstone is True),
            mark it as tombstone and keep it in store.
        """
        pass

    @abstractmethod
    async def delete_message(self, message_id: str) -> bool:
        """
        Delete a message from the store system.
        Returns True if a message was deleted, False otherwise
        """
        pass

    @abstractmethod
    async def get_message(self, message_id: str) -> MessageData | None:
        """
        Retrieve a stored message by its unique message_id.
        Returns a MessageData instance if found, or None otherwise.
        """
        pass

    @abstractmethod
    async def get_message_by_id(self, id: UUID) -> MessageData | None:
        """
        Retrieve a stored message by its primary key (id) in the MessageRawData table.
        Returns a MessageData instance if found, or None otherwise.
        """
        pass

    @abstractmethod
    async def get_thread_replies(
        self, channel_id: str, message_id: str
    ) -> list[MessageData]:
        """
        Gets all replies for a thread in a channel
        """
        pass

    @abstractmethod
    async def get_main_messages_before(
        self,
        channel_id: str,
        message_id: str,
        limit: int,
    ) -> list[MessageData]:
        """
        Gets main messages (not replies) that came before the given message in a channel
        """
        pass

    @abstractmethod
    async def get_main_messages_after(
        self,
        channel_id: str,
        message_id: str,
        limit: int,
    ) -> list[MessageData]:
        """
        Gets main messages (not replies) that came after the given message in a channel
        """
        pass

    @abstractmethod
    async def reconcile_channel_messages(
        self, source_slice: ChannelDataSlice
    ) -> ReconciliationStats:
        """
        Perform a full reconciliation between source messages and stored messages.

        This method serves as the main entry point for the reconciliation process.
        It computes the differences between the source data and stored data,
        then applies those differences to the database.
        """
        pass

    @abstractmethod
    async def get_channel_changelog(
        self,
        channel_id: str,
        last_cursor_position: int,
        batch_size: int = 100,
    ) -> list[MessageChangelogData]:
        """
        Retrieve changelog for a specific channel.

        Args:
            last_cursor_position: From cursor position
            batch_size: Maximum number of changes to retrieve
            channel_id: Identifier of the channel

        Returns:
            List of changes
        """
        pass
