from abc import ABC, abstractmethod
from dataclasses import dataclass, field

from app.integrations.schemas import DocumentData, MessageChangelogData


@dataclass
class DocumentChangeSet:
    documents_to_rebuild: set[str] = field(default_factory=set)
    documents_to_invalidate: set[str] = field(default_factory=set)


class IMessageToDocumentIndexer(ABC):
    @abstractmethod
    async def determine_document_changes(
        self, changes: list[MessageChangelogData]
    ) -> DocumentChangeSet:
        """
        Detects which documents need to be rebuilt based on the changes
        """
        pass

    @abstractmethod
    async def generate_document(self, message_id: str) -> DocumentData | None:
        """
        Generates a document for a main message
        """
        pass

    @abstractmethod
    async def invalidate_document(self, document_id: str) -> None:
        """
        Invalidates a document
        """
        pass
