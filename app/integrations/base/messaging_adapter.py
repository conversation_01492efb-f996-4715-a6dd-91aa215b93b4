import datetime
from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.adapter import BaseAdapter
from app.integrations.schemas import ChannelDataSlice


class BaseMessagingAdapter(BaseAdapter, ABC):
    @abstractmethod
    async def get_channel_data(
        self,
        channel_id: str,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
        batch_size: int = 100,
    ) -> ChannelDataSlice:
        pass

    @abstractmethod
    async def join_channel(
        self,
        channel_id: str,
    ) -> bool:
        pass

    @abstractmethod
    async def get_channel_info(
        self,
        channel_id: str,
    ) -> dict[str, Any]:
        pass
