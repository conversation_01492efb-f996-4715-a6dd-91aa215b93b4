from typing import Any

from app.common.helpers.logger import get_logger
from app.core.config import config
from app.integrations.backends.file.process_runner import File<PERSON>rocessRunner
from app.integrations.base.file_adapter import BaseFileAdapter
from app.integrations.base.file_backend import BaseFileBackend
from app.integrations.context import IntegrationContext
from app.integrations.processors.embedders.openai_embedder import OpenAIEmbedder
from app.integrations.schemas import DocumentData
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.types import ExtensionType, IntegrationSource

logger = get_logger()


class FileBackend(BaseFileBackend):
    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseFileAdapter],
        source: IntegrationSource,
        openai_api_key: str = config.openai_api_key,
    ):
        super().__init__(
            context=context,
            adapter_class=adapter_class,
            source=source,
        )
        self.process_runner = FileProcessRunner(
            context=context,
            source=source,
            adapter_class=adapter_class,
        )
        self.openai_api_key = openai_api_key

    async def start_processing(
        self,
        bucket_names: list[str],
        extensions: list[ExtensionType],
    ) -> dict[str, Any]:
        return await self.process_runner.run(
            bucket_names=bucket_names,
            extensions=extensions,
        )

    async def search_files(
        self,
        query: str,
        limit: int = 10,
    ) -> list[tuple[DocumentData, float]]:
        try:
            #  could be configurable in the future
            embedder = OpenAIEmbedder(api_key=self.openai_api_key)
            query_embedding = await embedder.embed_text(query)

            async with self.context.db_session_factory() as session:
                document_store = PostgresDocumentStore(
                    session=session,
                    tenant_id=self.context.tenant_id,
                    source=self.source,
                )

                result = await document_store.find_similar_documents(
                    embedding=query_embedding,
                    limit=limit,
                    tag_filter="file",
                )

                return result
        except Exception as e:
            logger.exception(f"Failed to search files: {e}")
            raise ValueError(f"Failed to search files: {str(e)}")
