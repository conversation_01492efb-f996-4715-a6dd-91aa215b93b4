from app.common.helpers.logger import get_logger
from app.common.task_runner.runner import TaskRunner
from app.core.config import config
from app.integrations.backends.file.file_processor import (
    FileProcessor,
)
from app.integrations.backends.file.process_stage import FileProcessStage
from app.integrations.base.file_adapter import BaseFileAdapter
from app.integrations.context import IntegrationContext
from app.integrations.processors.chunkers.recursive_chunker import RecursiveChunker
from app.integrations.processors.embedders.openai_embedder import OpenAIEmbedder
from app.integrations.processors.parsers.file_parser import FileParser
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.types import ExtensionType, IntegrationSource

logger = get_logger()


class FileProcessRunner:
    def __init__(
        self,
        context: IntegrationContext,
        source: IntegrationSource,
        adapter_class: type[BaseFileAdapter],
    ):
        self.context = context
        self.source = source
        self.tenant_id = context.tenant_id
        self.db_session_factory = context.db_session_factory
        self.adapter_class = adapter_class

    async def get_adapter(self) -> BaseFileAdapter:
        if not hasattr(self, "_adapter"):
            if not self.context.credentials_resolver:
                raise RuntimeError("Credentials Resolver is required.")
            credentials = await self.context.credentials_resolver.get_credentials(
                self.source
            )
            self._adapter = self.adapter_class(credentials=credentials)
        return self._adapter

    async def run(
        self,
        bucket_names: list[str],
        extensions: list[ExtensionType],
    ) -> dict:
        """
        Run file processing pipeline for the specified buckets and extensions.

        Args:
            bucket_names: Names of the buckets with files to process
            extensions: Extensions of the files to process

        Returns:
            Results of the pipeline execution
        """
        logger.info(
            f"Starting {self.source.value} files processing for tenant {self.tenant_id}"
        )

        adapter = await self.get_adapter()

        async with (
            self.db_session_factory() as doc_session,
            self.db_session_factory() as stage_session,
        ):
            document_store = PostgresDocumentStore(
                tenant_id=self.tenant_id,
                session=doc_session,
                source=self.source,
            )
            file_processor = FileProcessor(
                document_store=document_store,
                source=self.source,
                adapter=adapter,
                parser=FileParser(mistral_api_key=config.mistral_api_key),
                chunker=RecursiveChunker(chunk_size=2560, chunk_overlap=256),
                embedder=OpenAIEmbedder(api_key=config.openai_api_key),
            )

            process_stage = FileProcessStage(
                tenant_id=self.tenant_id,
                source=self.source,
                db_session=stage_session,
                processor=file_processor,
                bucket_names=bucket_names,
                extensions=extensions,
                stage_id=f"{self.source.value}_processor_for_{self.tenant_id}",
            )

            pipeline = TaskRunner()
            pipeline.add_task(process_stage)

            logger.info("Running file processing pipeline")
            results = await pipeline.run()
            logger.info("File processing pipeline execution completed")
            return results
