import datetime
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.base.calendar_adapter import BaseCalendarAdapter
from app.integrations.base.calendar_backend import BaseCalendarBackend
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import IntegrationContext
from app.integrations.types import IntegrationSource

logger = get_logger()


class CalendarBackend(BaseCalendarBackend):
    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseCalendarAdapter],
        source: IntegrationSource,
    ):
        super().__init__(context, adapter_class, source)

    async def _get_adapter(self) -> BaseCalendarAdapter:
        credentials_resolver: ICredentialsResolver = self.context.credentials_resolver
        credentials = await credentials_resolver.get_credentials(self.source)
        return self.adapter_class(credentials)

    async def list_calendars(self) -> list[dict[str, Any]]:
        adapter = await self._get_adapter()
        return await adapter.list_calendars()

    async def get_calendar(self, calendar_id: str) -> dict[str, Any]:
        adapter = await self._get_adapter()
        return await adapter.get_calendar(calendar_id)

    async def list_events(
        self,
        calendar_id: str,
        start_time: datetime.datetime | None = None,
        end_time: datetime.datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        adapter = await self._get_adapter()
        return await adapter.list_events(
            calendar_id=calendar_id,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
            single_events=single_events,
            order_by=order_by,
            show_deleted=show_deleted,
            page_token=page_token,
        )

    async def get_event(self, calendar_id: str, event_id: str) -> dict[str, Any]:
        adapter = await self._get_adapter()
        return await adapter.get_event(calendar_id, event_id)

    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_time: datetime.datetime | None = None,
        end_time: datetime.datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[dict[str, Any]]:
        adapter = await self._get_adapter()
        return await adapter.search_events(
            calendar_id=calendar_id,
            query=query,
            start_time=start_time,
            end_time=end_time,
            max_results=max_results,
            order_by=order_by,
        )

    async def get_user_info(self) -> dict[str, Any]:
        adapter = await self._get_adapter()
        return await adapter.get_user_info()

    async def get_upcoming_events(
        self,
        calendar_id: str,
        days_ahead: int = 7,
        max_results: int = 50,
    ) -> dict[str, Any]:
        now = datetime.datetime.now()
        end_time = now + datetime.timedelta(days=days_ahead)

        return await self.list_events(
            calendar_id=calendar_id,
            start_time=now,
            end_time=end_time,
            max_results=max_results,
            single_events=True,
            order_by="startTime",
        )

    async def get_events_for_date(
        self,
        calendar_id: str,
        date: datetime.date,
    ) -> dict[str, Any]:
        start_time = datetime.datetime.combine(date, datetime.time.min)
        end_time = datetime.datetime.combine(date, datetime.time.max)

        return await self.list_events(
            calendar_id=calendar_id,
            start_time=start_time,
            end_time=end_time,
            single_events=True,
            order_by="startTime",
        )
