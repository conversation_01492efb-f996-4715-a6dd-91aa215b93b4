from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.backends.messaging.ingest_runner import (
    MessagingIngestRunner,
)
from app.integrations.backends.messaging.process_runner import (
    MessagingProcessRunner,
)
from app.integrations.base.messaging_adapter import BaseMessagingAdapter
from app.integrations.base.messaging_backend import BaseMessagingBackend
from app.integrations.context import IntegrationContext
from app.integrations.processors.embedders.noop_embedder import No<PERSON><PERSON>mbedder
from app.integrations.schemas import DocumentData
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.types import IntegrationSource

logger = get_logger()


class MessagingBackend(BaseMessagingBackend):
    """
    Backend implementation for messaging integrations.

    This class provides a unified interface for messaging operations including:
    - Message search
    - Channel ingestion
    - Channel processing

    It contains all the common business logic and uses the messaging adapter
    only for platform-specific operations.
    """

    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseMessagingAdapter],
        source: IntegrationSource,
    ):
        super().__init__(
            context=context,
            adapter_class=adapter_class,
            source=source,
        )
        self.ingest_runner = MessagingIngestRunner(
            context=context,
            source=source,
            adapter_class=adapter_class,
        )
        self.process_runner = MessagingProcessRunner(
            context=context,
            source=source,
        )

    async def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        try:
            # Create embedder
            embedder = NoopEmbedder()  # Could be configurable in the future

            # Convert query to embedding
            query_embedding = await embedder.embed_text(query)

            # Create document store with its own session
            async with self.context.db_session_factory() as session:
                document_store = PostgresDocumentStore(
                    session=session,
                    tenant_id=self.context.tenant_id,
                    source=self.source,
                )

                # Add channel-specific tag for filtering
                channel_tag = f"channel_id:{channel_id}"

                # Search for documents
                results = await document_store.find_similar_documents(
                    embedding=query_embedding,
                    limit=limit,
                    tag_filter=channel_tag,
                )

                return results

        except Exception as e:
            logger.exception(f"Failed to search channel {channel_id}")
            raise ValueError(f"Failed to search channel: {str(e)}")

    async def start_channel_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        return await self.ingest_runner.run(
            channel_ids=channel_ids,
            interval_seconds=interval_seconds,
            lookback_days=lookback_days,
            batch_size=batch_size,
            daemon_mode=daemon_mode,
        )

    async def start_channel_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        return await self.process_runner.run(
            channel_ids=channel_ids,
            interval_seconds=interval_seconds,
            batch_size=batch_size,
            daemon_mode=daemon_mode,
        )
