import datetime

from app.common.helpers.logger import get_logger
from app.integrations.base.message_store import IMessageStore
from app.integrations.base.messaging_adapter import BaseMessagingAdapter
from app.integrations.schemas import ChannelIngestionResult
from app.integrations.types import IntegrationSource

logger = get_logger()


class MessagingChannelIngestor:
    """
    Ingests messages and replies from messaging channels and stores them using IMessageStore.

    This class handles:
    - Retrieving messages and threaded replies from messaging platforms within a configurable time window
    - Handling API rate limits through the platform adapter
    - Reconciling ingested data with existing store
    """

    def __init__(
        self,
        store: IMessageStore,
        adapter: BaseMessagingAdapter,
        source: IntegrationSource,
        batch_size: int = 100,
    ):
        self.store = store
        self.adapter = adapter
        self.source = source
        self.batch_size = batch_size

    async def ingest_channel(
        self,
        channel_id: str,
        start_time: datetime.datetime,
        end_time: datetime.datetime,
    ) -> ChannelIngestionResult:
        try:
            # Ensure we can access the channel first
            success = await self.adapter.join_channel(channel_id)
            if not success:
                logger.warning(
                    f"Could not join channel {channel_id} - limited functionality may be available"
                )

            # Get all messages from the channel including threaded replies
            # The adapter handles all the details of fetching and converting messages
            channel_slice = await self.adapter.get_channel_data(
                channel_id=channel_id,
                start_time=start_time,
                end_time=end_time,
                batch_size=self.batch_size,
            )

            # Reconcile with store
            stats = await self.store.reconcile_channel_messages(channel_slice)

            messages_len = len(channel_slice.messages)

            logger.info(
                f"Completed {self.source.value} ingestion for channel {channel_id} - "
                f"{messages_len} messages processed: "
                f"{stats.inserts} new, {stats.updates} updated, "
                f"{stats.deletes} deleted."
            )

            return ChannelIngestionResult(
                messages_count=messages_len,
                inserts=stats.inserts,
                updates=stats.updates,
                deletes=stats.deletes,
                from_time=start_time,
                to_time=end_time,
            )

        except Exception as e:
            logger.exception(f"Error ingesting channel {channel_id}")
            raise RuntimeError(f"Failed to ingest channel: {str(e)}") from e
