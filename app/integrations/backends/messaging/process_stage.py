import datetime
import uuid

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.helpers.logger import get_logger
from app.common.task_runner.base_task import BaseTask
from app.integrations.backends.messaging.channel_processor import (
    MessagingChannelProcessor,
)
from app.integrations.models import (
    MessagingProcessingRun,
)
from app.integrations.types import IntegrationSource

logger = get_logger()


class MessagingProcessStage(BaseTask):
    """
    Stage for processing data from messaging channels using a MessagingChannelProcessor.
    Tracking processing runs in a database.
    """

    def __init__(
        self,
        tenant_id: uuid.UUID,
        source: IntegrationSource,
        db_session: AsyncSession,
        processor: MessagingChannelProcessor,
        channel_ids: list[str],
        interval_seconds: int = 60,
        stage_id: str | None = None,
    ):
        """
        Initialize the messaging process stage.

        Args:
            tenant_id: The tenant
            source: Integration source type
            db_session: SQLAlchemy async session for tracking runs
            processor: The messaging channel processor to use
            channel_ids: List of channel IDs to process
            interval_seconds: Interval between execution cycles in seconds
            stage_id: Unique identifier for this stage
        """
        super().__init__(
            task_id=stage_id,
            interval_seconds=interval_seconds,
            logger=logger,
        )
        self.processor = processor
        self.channel_ids = channel_ids
        self.tenant_id = tenant_id
        self.source = source
        self.db_session = db_session
        self.metrics = {
            "total_runs": 0,
            "successful_runs": 0,
            "channels_processed": 0,
            "errors_count": 0,
        }

    async def execute_once(self) -> dict:
        """
        Execute one cycle of message processing for all configured channels.

        Returns:
            Dictionary with execution results and metrics that follows ExecutionResult structure
        """
        self.metrics["total_runs"] += 1
        results: dict = {
            "status": "success",
            "channels_processed": 0,
            "channels": {},
        }

        try:
            # Process each channel
            for channel_id in self.channel_ids:
                # Create processing run
                run = MessagingProcessingRun(
                    tenant_id=self.tenant_id,
                    source=self.source,
                    channel_id=channel_id,
                    status=MessagingProcessingRun.Status.IN_PROGRESS,
                    run_start=datetime.datetime.now(datetime.UTC),
                )
                self.db_session.add(run)
                await self.db_session.commit()

                try:
                    self.logger.info(
                        f"Processing {self.source.value} channel {channel_id}"
                    )

                    # Use the processor to handle this channel
                    channel_result = await self.processor.process_channel(channel_id)

                    run.status = MessagingProcessingRun.Status.SUCCESS
                    run.processed_changes = channel_result.processed_changes
                    run.regenerated_documents = channel_result.regenerated_documents
                    run.deleted_documents = channel_result.deleted_documents
                    run.run_end = datetime.datetime.now(datetime.UTC)
                    await self.db_session.commit()

                    # Update statistics
                    results["channels_processed"] += 1
                    self.metrics["channels_processed"] += 1

                    # Add channel-specific results
                    results["channels"][channel_id] = {
                        "status": "success",
                        **channel_result.model_dump(),
                    }

                    self.logger.info(
                        f"Successfully processed {self.source.value} channel {channel_id}"
                    )

                except Exception as e:
                    run.status = MessagingProcessingRun.Status.FAILED
                    error_msg = str(e)
                    run.error_message = error_msg[:1024]
                    run.run_end = datetime.datetime.now(datetime.UTC)
                    await self.db_session.commit()

                    error_msg = (
                        f"Error processing {self.source.value} channel {channel_id}"
                    )
                    self.logger.exception(error_msg)
                    results["channels"][channel_id] = {
                        "status": "error",
                        "error": str(e),
                    }
                    self.metrics["errors_count"] += 1

            # If any channel failed, mark overall status as partial
            if any(ch.get("status") == "error" for ch in results["channels"].values()):
                results["status"] = "partial"
            else:
                self.metrics["successful_runs"] += 1

            return results

        except Exception as e:
            self.logger.exception(f"Messaging processing stage failed: {self.task_id}")
            results["status"] = "error"
            results["error"] = str(e)
            return results

    def get_status(self) -> dict:
        """Get detailed status information including metrics."""
        status = super().get_status()
        status.update(
            {
                "tenant_id": str(self.tenant_id),
                "source": str(self.source),
                "channels_count": len(self.channel_ids),
                "interval_seconds": self.interval_seconds,
                "metrics": self.metrics,
            }
        )
        return status
