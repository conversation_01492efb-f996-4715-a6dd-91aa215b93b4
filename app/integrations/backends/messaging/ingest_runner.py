from app.common.helpers.logger import get_logger
from app.common.task_runner.runner import TaskRunner
from app.integrations.backends.messaging.channel_ingestor import (
    MessagingChannelIngestor,
)
from app.integrations.backends.messaging.ingest_stage import MessagingIngestStage
from app.integrations.base.messaging_adapter import BaseMessagingAdapter
from app.integrations.context import IntegrationContext
from app.integrations.stores.pg_message_store import PostgresMessageStore
from app.integrations.types import IntegrationSource

logger = get_logger()


class MessagingIngestRunner:
    """
    Runner for executing messaging data ingestion pipelines.

    This service handles the orchestration of data ingestion from
    messaging platforms into the system, encapsulating the complexity of pipeline
    creation and execution.
    """

    def __init__(
        self,
        context: IntegrationContext,
        source: IntegrationSource,
        adapter_class: type[BaseMessagingAdapter],
    ):
        """
        Initialize the MessagingIngestRunner.

        Args:
            context: Integration context with tenant_id and db_session_factory
            source: Integration source type
            adapter_class: Class to create messaging adapters
        """
        self.context = context
        self.source = source
        self.tenant_id = context.tenant_id
        self.db_session_factory = context.db_session_factory
        self.adapter_class = adapter_class

    async def get_adapter(self) -> BaseMessagingAdapter:
        if not hasattr(self, "_adapter"):
            if not self.context.credentials_resolver:
                raise RuntimeError("Credentials Resolver is required.")
            credentials = await self.context.credentials_resolver.get_credentials(
                self.source
            )
            self._adapter = self.adapter_class(credentials=credentials)
        return self._adapter

    async def run(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict:
        """
        Run channel ingestion pipeline for the specified channels.

        Args:
            channel_ids: List of channel IDs to ingest
            interval_seconds: Interval between executions in seconds
            lookback_days: Number of days to look back for messages
            batch_size: Number of messages per API call
            daemon_mode: Whether to run continuously in the background

        Returns:
            Results of the pipeline execution if not in daemon mode
            If daemon_mode is True, this method will block until the daemon is stopped,
            then return a status indicating the daemon has stopped.
        """
        logger.info(
            f"Starting channel ingestion for tenant {self.tenant_id} from {self.source.value}"
        )

        adapter = await self.get_adapter()

        # Create store and stage with their own sessions
        async with (
            self.db_session_factory() as store_session,
            self.db_session_factory() as stage_session,
        ):
            store = PostgresMessageStore(
                tenant_id=self.tenant_id,
                source=self.source,
                session=store_session,
            )

            # Create the channel ingestor
            ingestor = MessagingChannelIngestor(
                store=store,
                source=self.source,
                adapter=adapter,
                batch_size=batch_size,
            )

            # Create the ingest stage
            ingest_stage = MessagingIngestStage(
                tenant_id=self.tenant_id,
                source=self.source,
                db_session=stage_session,
                ingestor=ingestor,
                channel_ids=channel_ids,
                lookback_days=lookback_days,
                interval_seconds=interval_seconds,
                stage_id=f"{self.source.value}_ingestor_for_{self.tenant_id}",
            )

            pipeline = TaskRunner()
            pipeline.add_task(ingest_stage)

            # Execute the pipeline
            if daemon_mode:
                logger.info("Starting channel ingestion pipeline in daemon mode")
                await pipeline.start_daemon(wait=True)
                return {"status": "daemon_stopped"}
            else:
                logger.info("Running channel ingestion pipeline once")
                results = await pipeline.run()
                logger.info("Channel ingestion pipeline execution completed")
                return results
