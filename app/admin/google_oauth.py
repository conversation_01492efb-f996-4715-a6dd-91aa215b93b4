from typing import Union

from authlib.integrations.starlette_client import OAuth
from sqladmin.authentication import Authentication<PERSON>ackend
from starlette.requests import Request
from starlette.responses import RedirectResponse, Response

from app.core.config import config

oauth = OAuth()
oauth.register(
    "google",
    client_id=config.google_oauth_client_id,
    client_secret=config.google_oauth_client_secret,
    server_metadata_url="https://accounts.google.com/.well-known/openid-configuration",
    client_kwargs={
        "scope": "openid email profile",
        "prompt": "select_account",
    },
)
google_oauth_client = oauth.create_client("google")


class AdminGoogleAuth(AuthenticationBackend):
    async def login(self, _: Request) -> bool:
        return True

    async def logout(self, request: Request) -> bool:
        request.session.clear()
        return True

    async def authenticate(self, request: Request) -> Union[bool, RedirectResponse]:
        user = request.session.get("user")
        if not user:
            redirect_uri = request.url_for("login_google_route")
            return await google_oauth_client.authorize_redirect(request, redirect_uri)

        return True


admin_google_auth = AdminGoogleAuth(secret_key=config.session_secret_key)


async def login_google_route(request: Request) -> Response:
    token = await google_oauth_client.authorize_access_token(request)
    user = token.get("userinfo")
    if user:
        request.session["user"] = user
    return RedirectResponse(request.url_for("admin:index"))
