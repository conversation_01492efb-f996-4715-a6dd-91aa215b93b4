import pkgutil
import sys
import types

import pytest
from sqladmin import ModelView

from app.admin import _autodiscover_admin_views


# Dummy ModelView subclasses for testing
class DummyModel:
    pass


class DummyAdmin(ModelView):
    model = DummyModel


class AnotherDummyAdmin(ModelView):
    model = DummyModel


# Fixture to restore sys.modules after each test
@pytest.fixture(autouse=True)
def cleanup_sys_modules():
    original_modules = sys.modules.copy()
    yield
    sys.modules.clear()
    sys.modules.update(original_modules)


def create_dummy_module(
    name: str, code_globals: dict | None = None, is_package: bool = False
) -> types.ModuleType:
    """
    Create a dummy module with the given name and insert it into sys.modules.
    If is_package is True, set the __path__ attribute to simulate a package.
    """
    module = types.ModuleType(name)
    if is_package:
        # Setting a non-empty __path__ allows pkgutil.walk_packages to iterate.
        module.__path__ = ["/dummy_path"]
    if code_globals:
        module.__dict__.update(code_globals)
    sys.modules[name] = module
    return module


def test_autodiscover_non_package():
    """
    Test that a dummy module (not a package) with a ModelView subclass is discovered.
    """
    # Create dummy module "dummy.admin" which is not a package.
    dummy_globals = {"DummyAdmin": DummyAdmin}
    create_dummy_module("dummy.admin", dummy_globals, is_package=False)

    views = _autodiscover_admin_views(["dummy.admin"])
    assert DummyAdmin in views


def test_autodiscover_package(monkeypatch):
    """
    Test that a dummy package with a submodule containing a ModelView subclass is discovered.
    """
    # Create a dummy package "dummy_pkg.admin"
    package_name = "dummy_pkg.admin"
    submodule_name = "dummy_pkg.admin.sub"

    create_dummy_module(package_name, is_package=True)
    # Create submodule inside the package with AnotherDummyAdmin.
    sub_globals = {"AnotherDummyAdmin": AnotherDummyAdmin}
    create_dummy_module(submodule_name, sub_globals, is_package=False)

    # Monkey-patch pkgutil.walk_packages so that it returns our dummy submodule.
    def fake_walk_packages(_path, _prefix):
        yield (None, submodule_name, False)

    monkeypatch.setattr(pkgutil, "walk_packages", fake_walk_packages)

    views = _autodiscover_admin_views([package_name])
    assert AnotherDummyAdmin in views


def test_autodiscover_no_package_names(monkeypatch):
    """
    Test that autodiscover_admin_views works with no package_names provided.
    In this case, it will search for modules under the top-level "app" package.
    We simulate the 'app' package and fake pkgutil.walk_packages.
    """
    # Create a dummy top-level 'app' package.
    create_dummy_module("app", is_package=True)

    # Create a dummy admin module inside app: "app.some.admin".
    admin_module_name = "app.some.admin"
    admin_globals = {"DummyAdmin": DummyAdmin}
    create_dummy_module(admin_module_name, admin_globals, is_package=False)

    # Monkey-patch pkgutil.walk_packages to simulate finding "app.some.admin" when walking "app".
    def fake_walk_packages(_path, _prefix):
        yield (None, "app.some.admin", False)

    monkeypatch.setattr(pkgutil, "walk_packages", fake_walk_packages)

    views = _autodiscover_admin_views()
    assert DummyAdmin in views


def test_autodiscover_exclude_app_admin(monkeypatch):
    """
    Test that _autodiscover_admin_views excludes modules starting with 'app.admin'.
    In this test, we simulate the top-level 'app' package and fake pkgutil.walk_packages.
    """
    # Create the top-level 'app' package
    create_dummy_module("app", is_package=True)

    # Create a valid module that should be discovered: "app.some.admin"
    valid_module_name = "app.some.admin"
    create_dummy_module(valid_module_name, {"DummyAdmin": DummyAdmin}, is_package=False)

    # Create a module that should be excluded: "app.admin"
    invalid_module_name = "app.admin"
    create_dummy_module(
        invalid_module_name, {"ShouldNotBeIncluded": DummyAdmin}, is_package=False
    )

    # Monkey-patch pkgutil.walk_packages to simulate finding modules
    def fake_walk_packages(_path, _prefix):
        yield (None, invalid_module_name, False)
        yield (None, valid_module_name, False)

    monkeypatch.setattr(pkgutil, "walk_packages", fake_walk_packages)

    # Execute the autodiscovery function
    views = _autodiscover_admin_views()
    # Assert that only DummyAdmin from 'app.some.admin' is discovered
    assert DummyAdmin in views
    # Verify that only one view is discovered, excluding 'app.admin'
    assert len(views) == 1


def test_autodiscover_skips_without_model(monkeypatch):
    """
    Test that _autodiscover_admin_views does not register a ModelView subclass that lacks a model attribute.
    """

    # Create a module dummy in which we define a class without a model.
    class NotRegisteredAdmin(ModelView):
        pass

    module_name = "dummy.not_registered_admin"
    create_dummy_module(
        module_name, {"NotRegisteredAdmin": NotRegisteredAdmin}, is_package=False
    )

    # Monkey-patch pkgutil.walk_packages to simulate discovery of our module.
    def fake_walk_packages(_path, _prefix):
        yield (None, module_name, False)

    monkeypatch.setattr(pkgutil, "walk_packages", fake_walk_packages)

    views = _autodiscover_admin_views([module_name])
    # Verify that NotRegisteredAdmin is not discovered.
    assert NotRegisteredAdmin not in views
