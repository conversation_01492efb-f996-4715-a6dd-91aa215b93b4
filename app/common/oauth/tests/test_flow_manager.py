import time
import uuid
from urllib.parse import parse_qs, urlparse

import jwt
import pytest
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from app.common.oauth.exceptions import OAuthStateInvalidError, OAuthTokenError
from app.common.oauth.flow_manager import O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuthFlowType

SECRET_KEY = "test_secret_key"


@pytest.fixture
def oauth_flow_manager():
    return OAuthFlowManager(secret_key=SECRET_KEY)


@pytest.fixture
def oauth_flow_manager_custom_expiration():
    return OAuthFlowManager(secret_key=SECRET_KEY, state_expiration_seconds=300)


def test_init_with_custom_expiration():
    oauth_flow_manager = OAuthFlowManager(
        secret_key=SECRET_KEY, state_expiration_seconds=1800
    )
    assert oauth_flow_manager.state_expiration_seconds == 1800

    default_oauth_flow_manager = OAuthFlowManager(secret_key=SECRET_KEY)
    assert default_oauth_flow_manager.state_expiration_seconds == 600


def test_oauth_flow_manager_uses_default_config_secret_key(mocker):
    mock_config = mocker.Mock()
    mock_config.secret_key = "default_config_secret_key"

    mocker.patch("app.common.oauth.flow_manager.config", mock_config)

    flow_manager = OAuthFlowManager()
    assert flow_manager.secret_key == "default_config_secret_key"

    custom_key = "test_custom_key"
    custom_flow_manager = OAuthFlowManager(secret_key=custom_key)
    assert custom_flow_manager.secret_key == custom_key


def test_generate_authorization_uri_standard_flow(oauth_flow_manager: OAuthFlowManager):
    uid = str(uuid.uuid4())
    client_id = "test_client_id"
    redirect_uri = "https://example.com/callback"
    auth_url = "https://auth.example.com/oauth2/authorize"
    flow_type = OAuthFlowType.STANDARD
    scope = "test_scope"

    uri = oauth_flow_manager.generate_authorization_uri(
        uid=uid,
        client_id=client_id,
        redirect_uri=redirect_uri,
        auth_url=auth_url,
        flow_type=flow_type,
        scope=scope,
    )

    parsed_url = urlparse(uri)
    query_params = parse_qs(parsed_url.query)

    assert parsed_url.scheme + "://" + parsed_url.netloc + parsed_url.path == auth_url
    assert query_params["client_id"][0] == client_id
    assert query_params["redirect_uri"][0] == redirect_uri
    assert query_params["scope"][0] == scope
    assert query_params["response_type"][0] == "code"
    assert "state" in query_params
    assert "code_challenge" not in query_params

    state_token = query_params["state"][0]
    state_data = jwt.decode(state_token, SECRET_KEY, algorithms=["HS256"])
    assert state_data["uid"] == uid
    assert state_data["type"] == flow_type
    assert "ts" in state_data
    assert "nonce" in state_data
    assert "cv" not in state_data


def test_generate_authorization_uri_pkce_flow(oauth_flow_manager: OAuthFlowManager):
    uid = str(uuid.uuid4())
    client_id = "test_client_id"
    redirect_uri = "https://example.com/callback"
    auth_url = "https://auth.example.com/oauth2/authorize"
    flow_type = OAuthFlowType.PKCE
    scope = "test_scope"

    uri = oauth_flow_manager.generate_authorization_uri(
        uid=uid,
        client_id=client_id,
        redirect_uri=redirect_uri,
        auth_url=auth_url,
        flow_type=flow_type,
        scope=scope,
    )

    parsed_url = urlparse(uri)
    query_params = parse_qs(parsed_url.query)

    assert parsed_url.scheme + "://" + parsed_url.netloc + parsed_url.path == auth_url
    assert query_params["client_id"][0] == client_id
    assert query_params["redirect_uri"][0] == redirect_uri
    assert query_params["scope"][0] == scope
    assert query_params["response_type"][0] == "code"
    assert "state" in query_params
    assert "code_challenge" in query_params
    assert "code_challenge_method" in query_params
    assert query_params["code_challenge_method"][0] == "S256"

    state_token = query_params["state"][0]
    state_data = jwt.decode(state_token, SECRET_KEY, algorithms=["HS256"])
    assert state_data["uid"] == uid
    assert state_data["type"] == flow_type
    assert "ts" in state_data
    assert "nonce" in state_data
    assert "cv" in state_data


def test_validate_callback_state_valid(oauth_flow_manager: OAuthFlowManager):
    expected_uid = str(uuid.uuid4())
    state_data = {
        "type": OAuthFlowType.STANDARD,
        "ts": int(time.time()),
        "nonce": "test_nonce",
        "uid": expected_uid,
    }
    state = jwt.encode(state_data, SECRET_KEY, algorithm="HS256")

    is_valid, code_verifier = oauth_flow_manager.validate_callback_state(
        state, expected_uid
    )

    assert is_valid is True
    assert code_verifier == ""


def test_validate_callback_state_with_pkce(oauth_flow_manager: OAuthFlowManager):
    expected_uid = str(uuid.uuid4())
    code_verifier = "test_code_verifier"
    state_data = {
        "type": OAuthFlowType.PKCE,
        "ts": int(time.time()),
        "nonce": "test_nonce",
        "uid": expected_uid,
        "cv": code_verifier,
    }
    state = jwt.encode(state_data, SECRET_KEY, algorithm="HS256")

    is_valid, returned_code_verifier = oauth_flow_manager.validate_callback_state(
        state, expected_uid
    )

    assert is_valid is True
    assert returned_code_verifier == code_verifier


def test_validate_callback_state_expired(oauth_flow_manager: OAuthFlowManager):
    expected_uid = str(uuid.uuid4())
    state_data = {
        "type": OAuthFlowType.STANDARD,
        "ts": int(time.time()) - 601,
        "nonce": "test_nonce",
        "uid": expected_uid,
    }
    state = jwt.encode(state_data, SECRET_KEY, algorithm="HS256")

    is_valid, code_verifier = oauth_flow_manager.validate_callback_state(
        state, expected_uid
    )

    assert is_valid is False
    assert code_verifier == ""


def test_validate_callback_state_expired_custom_expiration(
    oauth_flow_manager_custom_expiration,
):
    expected_uid = str(uuid.uuid4())
    state_data = {
        "type": OAuthFlowType.STANDARD,
        "ts": int(time.time()) - 301,
        "nonce": "test_nonce",
        "uid": expected_uid,
    }
    state = jwt.encode(state_data, SECRET_KEY, algorithm="HS256")

    is_valid, code_verifier = (
        oauth_flow_manager_custom_expiration.validate_callback_state(
            state, expected_uid
        )
    )

    assert is_valid is False
    assert code_verifier == ""


def test_validate_callback_state_not_expired_custom_expiration(
    oauth_flow_manager_custom_expiration,
):
    expected_uid = str(uuid.uuid4())
    state_data = {
        "type": OAuthFlowType.STANDARD,
        "ts": int(time.time()) - 250,
        "nonce": "test_nonce",
        "uid": expected_uid,
    }
    state = jwt.encode(state_data, SECRET_KEY, algorithm="HS256")

    is_valid, code_verifier = (
        oauth_flow_manager_custom_expiration.validate_callback_state(
            state, expected_uid
        )
    )

    assert is_valid is True
    assert code_verifier == ""


def test_validate_callback_state_mismatched_uid(oauth_flow_manager: OAuthFlowManager):
    actual_uid = str(uuid.uuid4())
    expected_uid = str(uuid.uuid4())
    state_data = {
        "type": OAuthFlowType.STANDARD,
        "ts": int(time.time()),
        "nonce": "test_nonce",
        "uid": actual_uid,
    }
    state = jwt.encode(state_data, SECRET_KEY, algorithm="HS256")

    is_valid, code_verifier = oauth_flow_manager.validate_callback_state(
        state, expected_uid
    )

    assert is_valid is False
    assert code_verifier == ""


def test_validate_callback_state_invalid_token(oauth_flow_manager: OAuthFlowManager):
    expected_uid = str(uuid.uuid4())
    state = "invalid_token"

    is_valid, code_verifier = oauth_flow_manager.validate_callback_state(
        state, expected_uid
    )

    assert is_valid is False
    assert code_verifier == ""


@pytest.mark.anyio
async def test_exchange_code_for_token_success(
    oauth_flow_manager: OAuthFlowManager, mocker: MockerFixture
):
    expected_uid = str(uuid.uuid4())
    code = "test_code"
    code_verifier = "test_code_verifier"
    state_data = {
        "type": OAuthFlowType.PKCE,
        "ts": int(time.time()),
        "nonce": "test_nonce",
        "uid": expected_uid,
        "cv": code_verifier,
    }
    state = jwt.encode(state_data, SECRET_KEY, algorithm="HS256")
    client_id = "test_client_id"
    client_secret = "test_client_secret"
    redirect_uri = "https://example.com/callback"
    token_url = "https://auth.example.com/oauth2/token"

    mock_client = mocker.AsyncMock()
    mock_client.fetch_token.return_value = {
        "access_token": "mock_access_token",
        "refresh_token": "mock_refresh_token",
        "expires_in": 3600,
    }
    mocker.patch(
        "app.common.oauth.flow_manager.AsyncOAuth2Client", return_value=mock_client
    )

    token_data = await oauth_flow_manager.exchange_code_for_token(
        code=code,
        state=state,
        expected_uid=expected_uid,
        client_id=client_id,
        client_secret=client_secret,
        redirect_uri=redirect_uri,
        token_url=token_url,
    )

    mock_client.fetch_token.assert_called_once()
    assert token_data["access_token"] == "mock_access_token"
    assert token_data["refresh_token"] == "mock_refresh_token"
    assert token_data["expires_in"] == 3600


@pytest.mark.anyio
async def test_exchange_code_for_token_invalid_state(
    oauth_flow_manager: OAuthFlowManager,
):
    expected_uid = str(uuid.uuid4())
    code = "test_code"
    state = "invalid_state"
    client_id = "test_client_id"
    client_secret = "test_client_secret"
    redirect_uri = "https://example.com/callback"
    token_url = "https://auth.example.com/oauth2/token"

    with pytest.raises(OAuthStateInvalidError):
        await oauth_flow_manager.exchange_code_for_token(
            code=code,
            state=state,
            expected_uid=expected_uid,
            client_id=client_id,
            client_secret=client_secret,
            redirect_uri=redirect_uri,
            token_url=token_url,
        )


@pytest.mark.anyio
async def test_exchange_code_for_token_fetch_error(
    oauth_flow_manager: OAuthFlowManager, mocker: MockerFixture
):
    expected_uid = str(uuid.uuid4())
    code = "test_code"
    state_data = {
        "type": OAuthFlowType.STANDARD,
        "ts": int(time.time()),
        "nonce": "test_nonce",
        "uid": expected_uid,
    }
    state = jwt.encode(state_data, SECRET_KEY, algorithm="HS256")
    client_id = "test_client_id"
    client_secret = "test_client_secret"
    redirect_uri = "https://example.com/callback"
    token_url = "https://auth.example.com/oauth2/token"

    mock_client = mocker.AsyncMock()
    mock_client.fetch_token.side_effect = Exception("Token fetch error")
    mocker.patch(
        "app.common.oauth.flow_manager.AsyncOAuth2Client", return_value=mock_client
    )

    with pytest.raises(OAuthTokenError):
        await oauth_flow_manager.exchange_code_for_token(
            code=code,
            state=state,
            expected_uid=expected_uid,
            client_id=client_id,
            client_secret=client_secret,
            redirect_uri=redirect_uri,
            token_url=token_url,
        )


@pytest.mark.anyio
async def test_refresh_access_token_success(
    oauth_flow_manager: OAuthFlowManager, mocker: MockerFixture
):
    refresh_token = "test_refresh_token"
    client_id = "test_client_id"
    client_secret = "test_client_secret"
    token_url = "https://auth.example.com/oauth2/token"

    mock_client = mocker.AsyncMock()
    mock_client.refresh_token.return_value = {
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token",
        "expires_in": 3600,
    }
    mocker.patch(
        "app.common.oauth.flow_manager.AsyncOAuth2Client", return_value=mock_client
    )

    token_data = await oauth_flow_manager.refresh_access_token(
        refresh_token=refresh_token,
        client_id=client_id,
        client_secret=client_secret,
        token_url=token_url,
    )

    mock_client.refresh_token.assert_called_once_with(
        url=token_url,
        refresh_token=refresh_token,
    )
    assert token_data["access_token"] == "new_access_token"
    assert token_data["refresh_token"] == "new_refresh_token"
    assert token_data["expires_in"] == 3600


@pytest.mark.anyio
async def test_refresh_access_token_error(
    oauth_flow_manager: OAuthFlowManager, mocker: MockerFixture
):
    refresh_token = "test_refresh_token"
    client_id = "test_client_id"
    client_secret = "test_client_secret"
    token_url = "https://auth.example.com/oauth2/token"

    mock_client = mocker.AsyncMock()
    mock_client.refresh_token.side_effect = Exception("Token refresh error")
    mocker.patch(
        "app.common.oauth.flow_manager.AsyncOAuth2Client", return_value=mock_client
    )

    with pytest.raises(OAuthTokenError):
        await oauth_flow_manager.refresh_access_token(
            refresh_token=refresh_token,
            client_id=client_id,
            client_secret=client_secret,
            token_url=token_url,
        )
