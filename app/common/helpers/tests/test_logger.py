import inspect

from app.common.helpers.logger import get_logger


def test_get_logger_from_regular_module():
    logger = get_logger()
    assert logger.name == __name__


def test_get_logger_from_name():
    logger = get_logger("my-logger")
    assert logger.name == "my-logger"


def dummy_getmodule(*_args, **_kwargs):
    return None


def test_get_logger_from_main(monkeypatch):
    monkeypatch.setattr(inspect, "getmodule", dummy_getmodule)

    logger = get_logger()
    assert logger.name == "__main__"
