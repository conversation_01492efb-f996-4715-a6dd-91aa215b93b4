from fastapi import <PERSON><PERSON><PERSON>
from fastapi.testclient import TestClient

from app.common.exceptions import register_exception_handlers
from app.common.exceptions.base import (
    BadRequestException,
    ForbiddenException,
    NotFoundException,
    UnauthorizedException,
)

app = FastAPI()
register_exception_handlers(app)


@app.get("/raise-exception")
async def raise_exception():
    raise Exception("Test exception.")


def test_global_exception_handler():
    client = TestClient(app, raise_server_exceptions=False)
    response = client.get("/raise-exception")
    assert response.status_code == 500
    assert response.json() == {"detail": "Internal server error."}


@app.get("/unauthorized")
async def unauthorized():
    raise UnauthorizedException("Custom unauthorized message.")


def test_unauthorized_exception_handler():
    client = TestClient(app, raise_server_exceptions=False)
    response = client.get("/unauthorized")
    assert response.status_code == 401
    assert response.json() == {"detail": "Custom unauthorized message."}


@app.get("/unauthorized-default")
async def unauthorized_default():
    raise UnauthorizedException()


def test_unauthorized_exception_handler_default():
    client = TestClient(app, raise_server_exceptions=False)
    response = client.get("/unauthorized-default")
    assert response.status_code == 401
    assert response.json() == {"detail": "Authentication error."}


@app.get("/bad-request")
async def bad_request():
    raise BadRequestException("Custom bad request message.")


def test_bad_request_exception_handler():
    client = TestClient(app, raise_server_exceptions=False)
    response = client.get("/bad-request")
    assert response.status_code == 400
    assert response.json() == {"detail": "Custom bad request message."}


@app.get("/bad-request-default")
async def bad_request_default():
    raise BadRequestException()


def test_bad_request_exception_handler_default():
    client = TestClient(app, raise_server_exceptions=False)
    response = client.get("/bad-request-default")
    assert response.status_code == 400
    assert response.json() == {"detail": "Bad request."}


@app.get("/not-found")
async def not_found():
    raise NotFoundException("Custom not found message.")


def test_not_found_exception_handler():
    client = TestClient(app, raise_server_exceptions=False)
    response = client.get("/not-found")
    assert response.status_code == 404
    assert response.json() == {"detail": "Custom not found message."}


@app.get("/not-found-default")
async def not_found_default():
    raise NotFoundException()


def test_not_found_exception_handler_default():
    client = TestClient(app, raise_server_exceptions=False)
    response = client.get("/not-found-default")
    assert response.status_code == 404
    assert response.json() == {"detail": "Resource not found."}


@app.get("/forbidden")
async def forbidden():
    raise ForbiddenException("Forbidden access.")


def test_forbidden_exception_handler():
    client = TestClient(app, raise_server_exceptions=False)
    response = client.get("/forbidden")
    assert response.status_code == 403
    assert response.json() == {"detail": "Forbidden access."}
