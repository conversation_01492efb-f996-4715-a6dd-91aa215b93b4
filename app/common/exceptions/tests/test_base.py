from app.common.exceptions.base import (
    BadRequestException,
    ForbiddenException,
    NotFoundException,
    UnauthorizedException,
    _BaseException,
)


def test_base_exception_default_message():
    exc = _BaseException()
    assert str(exc) == "An error occurred."
    assert exc.detail == "An error occurred."


def test_base_exception_custom_message():
    exc = _BaseException("Custom message.")
    assert str(exc) == "Custom message."
    assert exc.detail == "Custom message."


def test_unauthorized_exception_default_message():
    exc = UnauthorizedException()
    assert str(exc) == "Authentication error."
    assert exc.detail == "Authentication error."


def test_unauthorized_exception_custom_message():
    exc = UnauthorizedException("Custom unauthorized message.")
    assert str(exc) == "Custom unauthorized message."
    assert exc.detail == "Custom unauthorized message."


def test_bad_request_exception_default_message():
    exc = BadRequestException()
    assert str(exc) == "Bad request."
    assert exc.detail == "Bad request."


def test_bad_request_exception_custom_message():
    exc = BadRequestException("Custom bad request message.")
    assert str(exc) == "Custom bad request message."
    assert exc.detail == "Custom bad request message."


def test_not_found_exception_default_message():
    exc = NotFoundException()
    assert str(exc) == "Resource not found."
    assert exc.detail == "Resource not found."


def test_not_found_exception_custom_message():
    exc = NotFoundException("Custom not found message.")
    assert str(exc) == "Custom not found message."
    assert exc.detail == "Custom not found message."


def test_forbidden_exception_default_message():
    exc = ForbiddenException()
    assert str(exc) == "Forbidden access."
    assert exc.detail == "Forbidden access."


def test_forbidden_exception_custom_message():
    exc = ForbiddenException("Custom forbidden message.")
    assert str(exc) == "Custom forbidden message."
    assert exc.detail == "Custom forbidden message."


def test_exception_inheritance():
    exc = UnauthorizedException()
    assert isinstance(exc, _BaseException)
    assert isinstance(exc, Exception)

    exc = BadRequestException()
    assert isinstance(exc, _BaseException)
    assert isinstance(exc, Exception)

    exc = NotFoundException()
    assert isinstance(exc, _BaseException)
    assert isinstance(exc, Exception)
