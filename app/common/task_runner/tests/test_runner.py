import pytest

from app.common.task_runner.base_task import BaseTask
from app.common.task_runner.sequential_task import SequentialTask


class MockTask(BaseTask):
    def __init__(self, name, task_id=None):
        super().__init__(task_id)
        self.name = name
        self.executed = False

    async def execute_once(self):
        self.executed = True
        return f"Result from {self.name}"


@pytest.mark.anyio
async def test_sequential_task_execution():
    task1 = MockTask("Task 1")
    task2 = MockTask("Task 2")

    sequential_task = SequentialTask([task1, task2])
    results = await sequential_task.execute_once()

    assert len(results) == 2
    assert all(isinstance(key, str) for key in results)
    assert task1.executed
    assert task2.executed


def test_sequential_task_stop():
    task1 = MockTask("Task 1")
    task2 = MockTask("Task 2")

    sequential_task = SequentialTask([task1, task2])
    sequential_task.stop()

    assert sequential_task.should_stop
    assert task1.should_stop
    assert task2.should_stop


def test_sequential_task_get_status():
    task1 = MockTask("Task 1")
    task2 = MockTask("Task 2")

    sequential_task = SequentialTask([task1, task2], interval_seconds=30)
    status = sequential_task.get_status()

    assert status["tasks_count"] == 2
    assert status["interval_seconds"] == 30
    assert len(status["child_tasks"]) == 2

    assert status["child_tasks"][0]["id"] != status["child_tasks"][1]["id"]

    assert all(task["name"] == "MockTask" for task in status["child_tasks"])
