import pytest

from app.common.task_runner.base_task import BaseTask


class MockTask(BaseTask):
    def __init__(self, execute_count=1, task_id=None):
        super().__init__(task_id)
        self.execute_count = execute_count
        self.current_count = 0
        self.executed_results = []

    async def execute_once(self):
        self.current_count += 1
        result = f"Execution {self.current_count}"
        self.executed_results.append(result)
        return result


@pytest.mark.anyio
async def test_base_task_execute_once():
    task = MockTask()
    result = await task.execute_once()
    assert result == "Execution 1"


def test_base_task_stop():
    task = MockTask()
    task.stop()
    assert task.should_stop is True


def test_base_task_get_status():
    task = MockTask(task_id="test-id")
    status = task.get_status()
    assert status == {"id": "test-id", "name": "MockTask", "should_stop": False}


def test_base_task_unique_id():
    task1 = MockTask()
    task2 = MockTask()
    assert task1.task_id != task2.task_id


@pytest.mark.anyio
async def test_base_task_execute_with_stop(mocker):
    task = MockTask()

    mocker.patch("asyncio.sleep", return_value=None)

    async def mock_execute_once():
        task.should_stop = True
        return "Stopping"

    task.execute_once = mock_execute_once

    await task.execute()
    assert task.should_stop is True
