import uuid
from datetime import datetime

import pytest
from sqlalchemy import String
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Mapped, mapped_column

from app.common.orm.base_async_repository import BaseAsyncRepository
from app.core.database import BaseModel


class DummyModel(BaseModel):
    __tablename__ = "async_dummy_model"

    name: Mapped[str] = mapped_column(String)
    value: Mapped[str | None] = mapped_column(String, nullable=True)


@pytest.fixture(autouse=True)
async def add_dummy_model_async(async_engine):
    dummy_table = DummyModel.metadata.tables[DummyModel.__tablename__]

    async with async_engine.begin() as connection:
        await connection.run_sync(
            lambda sync_conn: DummyModel.metadata.create_all(
                sync_conn, tables=[dummy_table]
            )
        )


@pytest.fixture
async def repository(
    async_db_session: AsyncSession,
) -> BaseAsyncRepository[DummyModel]:
    return BaseAsyncRepository(async_db_session, DummyModel)


@pytest.mark.anyio
async def test_create(repository: BaseAsyncRepository[DummyModel]):
    instance = await repository.create(name="test", value="test_value")
    assert isinstance(instance.id, uuid.UUID)
    assert isinstance(instance.created_at, datetime)
    assert isinstance(instance.updated_at, datetime)
    assert instance.name == "test"
    assert instance.value == "test_value"


@pytest.mark.anyio
async def test_get_by_id(repository: BaseAsyncRepository[DummyModel]):
    instance = await repository.create(name="test")
    fetched = await repository.get_by_id(instance.id)
    assert fetched is not None
    assert fetched.id == instance.id
    assert fetched.name == instance.name

    non_existent = await repository.get_by_id(uuid.uuid4())
    assert non_existent is None


@pytest.mark.anyio
async def test_get_all(repository: BaseAsyncRepository[DummyModel]):
    await repository.create(name="test1")
    await repository.create(name="test2")

    instances = await repository.get_all()
    assert len(instances) == 2
    assert all(isinstance(instance.id, uuid.UUID) for instance in instances)


@pytest.mark.anyio
async def test_get_by_ids(repository: BaseAsyncRepository[DummyModel]):
    instance1 = await repository.create(name="test1")
    instance2 = await repository.create(name="test2")

    instances = await repository.get_by_ids([instance1.id, instance2.id])
    assert len(instances) == 2
    assert {instance.id for instance in instances} == {instance1.id, instance2.id}

    # Test empty list
    assert await repository.get_by_ids([]) == []


@pytest.mark.anyio
async def test_update(repository: BaseAsyncRepository[DummyModel]):
    instance = await repository.create(name="test")
    updated = await repository.update(instance.id, name="updated")
    assert updated.name == "updated"

    with pytest.raises(ValueError):
        await repository.update(uuid.uuid4(), name="non_existent")


@pytest.mark.anyio
async def test_delete(repository: BaseAsyncRepository[DummyModel]):
    instance = await repository.create(name="test")
    await repository.delete(instance.id)
    assert await repository.get_by_id(instance.id) is None

    with pytest.raises(ValueError):
        await repository.delete(uuid.uuid4())


@pytest.mark.anyio
async def test_invalid_attribute(repository: BaseAsyncRepository[DummyModel]):
    with pytest.raises(AttributeError):
        await repository.create(invalid_attr="test")

    instance = await repository.create(name="test")
    with pytest.raises(AttributeError):
        await repository.update(instance.id, invalid_attr="test")


@pytest.mark.anyio
async def test_get_by_attrs(repository: BaseAsyncRepository[DummyModel]):
    await repository.create(name="test1", value="value1")
    await repository.create(name="test2", value="value2")
    await repository.create(name="test1", value="value3")

    results = await repository._get_by_attrs(name="test1")
    assert len(results) == 2
    assert all(r.name == "test1" for r in results)

    results = await repository._get_by_attrs(name="test1", value="value1")
    assert len(results) == 1
    assert results[0].value == "value1"
